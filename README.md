# SCHNELLIOT API - FastAPI Version

A modern IoT management system for smart lighting infrastructure, converted from Django to FastAPI.

## Overview

SCHNELLIOT API is a comprehensive IoT management platform that provides:

- **Lume Module**: Luminator APIs for device management, control, and monitoring
- **Onboarder Module**: Device onboarding for ILM and Gateway devices
- **SMS Tool Module**: SMS service integration for notifications and alerts

## Features

- 🚀 **High Performance**: Built with FastAPI for superior performance
- 🔒 **Secure**: Token-based authentication and authorization
- 📊 **IoT Integration**: ThingsBoard platform integration
- 🌐 **RESTful APIs**: Comprehensive REST API endpoints
- 🐳 **Containerized**: Docker and Docker Compose support
- 📱 **Mobile Ready**: Optimized for mobile applications
- 🔄 **Real-time**: WebSocket support for real-time updates

## Architecture

```
mobaile_api/
├── __init__.py
├── main.py                 # FastAPI application entry point
├── config.py              # Configuration management
├── database.py            # Database configuration
├── lume/                  # Luminator APIs module
│   ├── __init__.py
│   ├── router.py          # Main Lume routes
│   ├── customer_router.py # Customer & location routes
│   ├── device_router.py   # Device control routes
│   ├── schemas.py         # Pydantic models
│   ├── service.py         # Business logic
│   └── dependencies.py    # FastAPI dependencies
├── onboarder/             # Device onboarding module
│   ├── __init__.py
│   ├── router.py          # Onboarding routes
│   ├── schemas.py         # Pydantic models
│   └── service.py         # Onboarding services
└── smstool/               # SMS services module
    ├── __init__.py
    ├── router.py          # SMS routes
    └── cochin_service.py  # Cochin CSML integration
```

## Quick Start

### Prerequisites

- Python 3.9+
- MySQL 8.0+
- Docker & Docker Compose (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd schnelliot-api
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**
   ```bash
   cp mobaile_api/.env.example mobaile_api/.env
   # Edit .env file with your configuration
   ```

5. **Run the application**
   ```bash
   uvicorn mobaile_api.main:app --reload
   ```

### Docker Deployment

1. **Using Docker Compose**
   ```bash
   docker-compose up -d
   ```

2. **Build and run manually**
   ```bash
   docker build -t schnelliot-api .
   docker run -p 8000:8000 schnelliot-api
   ```

## Configuration

### Environment Variables

Create a `.env` file in the `mobaile_api` directory:

```env
# Application
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DATABASE_URL=mysql+pymysql://user:password@localhost:3306/schnelliot_db
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=schnelliot_db

# ThingsBoard
BASE_URL=https://your-thingsboard-url
LOGIN_CREDENTIALS={"username": "your-username", "password": "your-password"}

# SMS Services
COCHIN_CSML_USERNAME=your-cochin-username
COCHIN_CSML_PASSWORD=your-cochin-password
COCHIN_CSML_URL=https://your-cochin-url
GHMC_SMS_URL=https://your-ghmc-sms-url
GHMC_SMS_USERNAME=your-ghmc-username
GHMC_SMS_PASSWORD=your-ghmc-password

# AWS
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-s3-bucket

# Google Cloud
GOOGLE_CLOUD_PROJECT=your-gcp-project
PUBSUB_TOPIC=your-pubsub-topic
```

## API Documentation

Once the application is running, visit:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Main API Endpoints

#### Authentication
- `POST /api/login/` - User login

#### Device Management
- `GET /api/get/device/` - Get device details
- `POST /api/device/service/` - Device service operations
- `POST /api/ilm/test/` - ILM device testing
- `POST /api/ilm/control/` - ILM device control
- `POST /api/gw/install/` - Gateway installation
- `POST /api/gw/control/` - Gateway control

#### Entity Management
- `GET /api/entities/search/` - Search entities
- `GET /api/entity/detail/` - Get entity details
- `GET /api/get/assets/` - Get asset information

#### Location Services
- `GET /api/customers/` - Get customers
- `GET /api/regions/` - Get regions
- `GET /api/zones/` - Get zones
- `GET /api/wards/` - Get wards

#### Device Onboarding
- `POST /onboard/ilm/{credentials}/{variant}/` - Onboard ILM device
- `POST /onboard/gw/{imei_no}/{variant}/` - Onboard Gateway device

#### SMS Services
- `POST /sms/send/` - Send SMS
- `POST /sms/ghmc/tran_ghmc/` - GHMC transaction
- `POST /sms/ghmc/tran_ap/` - AP transaction

## Development

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black mobaile_api/
isort mobaile_api/
flake8 mobaile_api/
```

### Database Migrations

The application uses SQLAlchemy for database operations. Tables are created automatically on startup.

## Deployment

### Production Deployment

1. **Set production environment variables**
2. **Use a production ASGI server**
   ```bash
   gunicorn mobaile_api.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```
3. **Configure reverse proxy (Nginx)**
4. **Set up SSL certificates**
5. **Configure monitoring and logging**

### Health Checks

- **Health endpoint**: `GET /health`
- **Root endpoint**: `GET /`

## Migration from Django

This FastAPI version maintains API compatibility with the original Django implementation while providing:

- Better performance and scalability
- Modern async/await support
- Automatic API documentation
- Type safety with Pydantic
- Improved error handling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[Your License Here]

## Support

For support and questions, please contact [Your Contact Information].

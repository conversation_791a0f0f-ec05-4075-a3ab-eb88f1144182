{"info": {"_postman_id": "schnelliot-api-fastapi", "name": "SCHNELLIOT API - FastAPI", "description": "Complete API collection for SCHNELLIOT IoT Management System (FastAPI Version)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "admin", "type": "text"}, {"key": "password", "value": "password", "type": "text"}, {"key": "latitude", "value": "17.3850", "type": "text"}, {"key": "longitude", "value": "78.4867", "type": "text"}]}, "url": {"raw": "{{base_url}}/lume/api/login/", "host": ["{{base_url}}"], "path": ["lume", "api", "login", ""]}}, "response": []}, {"name": "Get User Role (Print QR)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/lume/api/print_qr/", "host": ["{{base_url}}"], "path": ["lume", "api", "print_qr", ""]}}, "response": []}]}, {"name": "Device Management", "item": [{"name": "Get Device Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/lume/api/get/device/?deviceId=device-id-here", "host": ["{{base_url}}"], "path": ["lume", "api", "get", "device", ""], "query": [{"key": "deviceId", "value": "device-id-here"}]}}, "response": []}, {"name": "Device Service", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceId\": \"device-id-here\",\n    \"serviceType\": \"maintenance\"\n}"}, "url": {"raw": "{{base_url}}/lume/api/device/service/", "host": ["{{base_url}}"], "path": ["lume", "api", "device", "service", ""]}}, "response": []}]}, {"name": "Entity Management", "item": [{"name": "Search Entities", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/lume/api/entities/search/?entityType=DEVICE&searchText=test&pageSize=20&page=0", "host": ["{{base_url}}"], "path": ["lume", "api", "entities", "search", ""], "query": [{"key": "entityType", "value": "DEVICE"}, {"key": "searchText", "value": "test"}, {"key": "pageSize", "value": "20"}, {"key": "page", "value": "0"}]}}, "response": []}, {"name": "Get Entity Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/lume/api/entity/detail/?entityId=entity-id-here&entityType=DEVICE", "host": ["{{base_url}}"], "path": ["lume", "api", "entity", "detail", ""], "query": [{"key": "entityId", "value": "entity-id-here"}, {"key": "entityType", "value": "DEVICE"}]}}, "response": []}]}, {"name": "Location Services", "item": [{"name": "Get Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/lume/api/customers/", "host": ["{{base_url}}"], "path": ["lume", "api", "customers", ""]}}, "response": []}, {"name": "Get Regions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/lume/api/regions/?customerId=customer-id-here", "host": ["{{base_url}}"], "path": ["lume", "api", "regions", ""], "query": [{"key": "customerId", "value": "customer-id-here"}]}}, "response": []}, {"name": "Get Zones", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/lume/api/zones/?regionId=region-id-here", "host": ["{{base_url}}"], "path": ["lume", "api", "zones", ""], "query": [{"key": "regionId", "value": "region-id-here"}]}}, "response": []}, {"name": "Get <PERSON>s", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/lume/api/wards/?zoneId=zone-id-here", "host": ["{{base_url}}"], "path": ["lume", "api", "wards", ""], "query": [{"key": "zoneId", "value": "zone-id-here"}]}}, "response": []}]}, {"name": "Gateway Control", "item": [{"name": "Gateway Dispatch", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "panelId", "value": "panel-123", "type": "text"}, {"key": "gwType", "value": "gateway-type", "type": "text"}, {"key": "ebMeterNo", "value": "meter-456", "type": "text"}, {"key": "phase", "value": "3", "type": "text"}, {"key": "simNo", "value": "sim-789", "type": "text"}]}, "url": {"raw": "{{base_url}}/lume/api/gw/dispatch/", "host": ["{{base_url}}"], "path": ["lume", "api", "gw", "dispatch", ""]}}, "response": []}, {"name": "Gateway Install", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceId\": \"gateway-device-id\",\n    \"installationData\": {\n        \"location\": \"Test Location\",\n        \"coordinates\": {\n            \"lat\": 17.3850,\n            \"lng\": 78.4867\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/lume/api/gw/install/", "host": ["{{base_url}}"], "path": ["lume", "api", "gw", "install", ""]}}, "response": []}, {"name": "Gateway Control", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceId\": \"gateway-device-id\",\n    \"command\": \"restart\",\n    \"parameters\": {\n        \"delay\": 5\n    }\n}"}, "url": {"raw": "{{base_url}}/lume/api/gw/control/", "host": ["{{base_url}}"], "path": ["lume", "api", "gw", "control", ""]}}, "response": []}]}, {"name": "ILM Control", "item": [{"name": "ILM Test", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "deviceId", "value": "ilm-device-id", "type": "text"}, {"key": "action", "value": "test_brightness", "type": "text"}, {"key": "jigNumber", "value": "jig-123", "type": "text"}]}, "url": {"raw": "{{base_url}}/lume/api/ilm/test/", "host": ["{{base_url}}"], "path": ["lume", "api", "ilm", "test", ""]}}, "response": []}, {"name": "ILM Control", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deviceId\": \"ilm-device-id\",\n    \"command\": \"set_brightness\",\n    \"parameters\": {\n        \"brightness\": 80\n    }\n}"}, "url": {"raw": "{{base_url}}/lume/api/ilm/control/", "host": ["{{base_url}}"], "path": ["lume", "api", "ilm", "control", ""]}}, "response": []}]}, {"name": "<PERSON><PERSON> Onboarding", "item": [{"name": "Onboard ILM Device", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/onboard/ilm/1234567890123456/ilm_v1/", "host": ["{{base_url}}"], "path": ["onboard", "ilm", "1234567890123456", "ilm_v1", ""]}}, "response": []}, {"name": "Onboard Gateway Device", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/onboard/gw/123456789012345/gateway_v1/", "host": ["{{base_url}}"], "path": ["onboard", "gw", "123456789012345", "gateway_v1", ""]}}, "response": []}]}, {"name": "SMS Services", "item": [{"name": "Send SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "mobile", "value": "**********", "type": "text"}, {"key": "message", "value": "Test SMS message", "type": "text"}]}, "url": {"raw": "{{base_url}}/sms/send/", "host": ["{{base_url}}"], "path": ["sms", "send", ""]}}, "response": []}, {"name": "GHMC Transaction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "mobile", "value": "**********", "type": "text"}, {"key": "message", "value": "GHMC transaction message", "type": "text"}]}, "url": {"raw": "{{base_url}}/sms/ghmc/tran_ghmc/", "host": ["{{base_url}}"], "path": ["sms", "ghmc", "tran_ghmc", ""]}}, "response": []}]}, {"name": "Health & Status", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "Root Endpoint", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}}, "response": []}]}]}
version: '3.8'

services:
  # FastAPI Application
  api:
    build: .
    container_name: schnelliot-api
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - DATABASE_URL=mysql+pymysql://root:password@db:3306/schnelliot_db
      - MYSQL_HOST=db
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=password
      - MYSQL_DATABASE=schnelliot_db
      - BASE_URL=https://your-thingsboard-url
      - LOGIN_CREDENTIALS={"username": "your-username", "password": "your-password"}
      - COCHIN_CSML_USERNAME=your-cochin-username
      - COCHIN_CSML_PASSWORD=your-cochin-password
      - COCHIN_CSML_URL=https://your-cochin-url
      - GHMC_SMS_URL=https://your-ghmc-sms-url
      - GHMC_SMS_USERNAME=your-ghmc-username
      - GHMC_SMS_PASSWORD=your-ghmc-password
      - AWS_ACCESS_KEY_ID=your-aws-access-key
      - AWS_SECRET_ACCESS_KEY=your-aws-secret-key
      - AWS_REGION=us-east-1
      - S3_BUCKET_NAME=your-s3-bucket
      - GOOGLE_CLOUD_PROJECT=your-gcp-project
      - PUBSUB_TOPIC=your-pubsub-topic
    volumes:
      - ./mobaile_api:/app/mobaile_api
      - ./logs:/app/logs
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - schnelliot-network

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: schnelliot-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=schnelliot_db
      - MYSQL_USER=schnelliot
      - MYSQL_PASSWORD=schnelliot123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    restart: unless-stopped
    networks:
      - schnelliot-network

  # Redis (for caching and session management)
  redis:
    image: redis:7-alpine
    container_name: schnelliot-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - schnelliot-network

  # Nginx (reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: schnelliot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - schnelliot-network

volumes:
  mysql_data:
  redis_data:

networks:
  schnelliot-network:
    driver: bridge

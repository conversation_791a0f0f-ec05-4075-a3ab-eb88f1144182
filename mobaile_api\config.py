"""
FastAPI Configuration
Converted from Django settings.py
"""

import os
import json
from typing import List, Optional
from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """Application settings using Pydantic BaseSettings"""
    
    # Database Configuration
    db_name: str
    db_user: str
    db_password: str
    db_host: str = "localhost"
    db_port: int = 3306
    
    # Application Configuration
    secret_key: str
    debug: bool = False
    allowed_hosts: List[str] = ["*"]
    
    # ThingsBoard Configuration
    base_url: str
    login_credentials: str
    
    # CSML Configuration
    csml_login_url: Optional[str] = None
    csml_lamp_failure_url: Optional[str] = None
    
    # CORS Configuration
    cors_allowed_origins: str = ""
    
    # Timezone
    time_zone: str = "Asia/Kolkata"
    
    # Media Configuration
    media_root: str = "Media"
    media_url: str = "Media/"
    
    # AWS Configuration
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_storage_bucket_name: Optional[str] = None
    
    # Google Cloud Configuration
    google_application_credentials: Optional[str] = None
    
    # Logging
    log_level: str = "INFO"
    
    @validator('cors_allowed_origins')
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',') if origin.strip()]
        return v
    
    @validator('login_credentials')
    def parse_login_credentials(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return {"username": "", "password": ""}
        return v
    
    @validator('allowed_hosts')
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(',') if host.strip()]
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Application Constants (from Django settings)
ILM_SERVER_ATTRIBUTES = "testResults,PDI,state,condition,dimmable,active,wardName,zoneName,region,qrCount"
LUME_PRINT_QR_REQUIRED_ROLE = "production manager"
ILM_ATTRIBUTE_TYPE_CONVERT_BOOL = [
    "dimmable", "PDI", "brightness_100", "brightness_70", "brightness_50",
    "brightness_30", "brightness_0", "flash", "rtc", "active"
]
ILM_ATTRIBUTE_TYPE_CONVERT_INT = [
    "qrCount", "error_count", "start_ts", "end_ts", "lastActivityTime", 
    "installedOn", "lampWatts", "armCount", "lampWattage"
]
ILM_ATTRIBUTE_TYPE_CONVERT_DOUBLE = ["latitude", "longitude", "accuracy", "slatitude", "slongitude"]
GW_SERVER_ATTRIBUTES = "active,state,condition,wardName,zoneName,region"
ENTITY_TYPE = ["DEVICE", "ASSET"]
ENTITY_TYPE_ASSET = "ASSET"
ENTITY_TYPE_DEVICE = "DEVICE"
ATTRIBUTE_SCOPES = ["CLIENT_SCOPE", "SERVER_SCOPE", "SHARED_SCOPE"]
DEVICE_TYPES = ("gw", "ilm", "ilm-4g", "ccms", "nic", "smslc")
RELATION_TYPES = ["Contains", "ControlledBy", "LitBy", "Powers", "Routes", "Mounts", "CanAccess"]
ILM_TEST_QUERY_PARAMS = ["jigNumber", "deviceId", "action"]
GW_ENTITY_ID_KEY = ["deviceId"]
ENTITY_RELATIONS = ["FROM", "TO"]
GW_DISPATCH_QUERY_PARAMS = ["panelId", "gwType", "ebMeterNo", "phase", "simNo"]
GW_ASSET_TYPES = ("ccms", "hub", "smslc")
ILM_ASSET_TYPES = ["lamp", "lightPoint", "region", "zone", "ward", "pole"]

LIGHT_POINT_INSTALLATION_ATTRIBUTES = [
    "latitude", "longitude", "accuracy", "landmark", "wardName", "zoneName",
    "region", "installedOn", "installedBy", "lampWatts"
]

GW_INSTALLATION_ATTRIBUTES = [
    "vehicleAccessAvailable", "connection", "earthingRequired", "controlWireStatus", 
    "armDetails", "accuracy", "manualSwitchControl", "remarks", "signalStrength", 
    "region", "zoneName", "sector", "wardName", "installedBy", "installedOn",
    "roadCategory", "roadWidth", "incomingTransmissionLine", "incomingTransmissionType", 
    "bracketMountingHeight", "state", "locationDetails", "latitude", "longitude", 
    "location", "armCount", "auditImg", "assetType", "roadType"
]

LAMP_INSTALLATION_ATTRIBUTES = [
    "lampWatts", "manufacturer", "lampType", "year", "wardName", "installedOn",
    "installedBy", "dimmable", "state", "condition"
]

DEVICE_FAILURE_STATUS_HANDLER = [200, 401]
COMMON_PARAMS = "lastActivityTime"
LIGHT_POINT_ASSET_FORMAT = "LP-{asset_name}"
LAMP_ASSET_FORMAT = "LMP-{asset_name}"
AVAILABLE_STATES = ["TESTABLE", "TESTED", "INSTALLABLE", "INSTALLED"]
AVAILABLE_CONDITION = ["NEW", "SERVICE", "SCRAPPED"]
REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION = {"state": "INSTALLABLE"}
LAMP_INSTALLED_STATE_CONDITION = {"state": "INSTALLED", "condition": "NEW"}
INSTALLED_STATE = {"state": "INSTALLED"}
OWNER_TYPE = ["CUSTOMER", "TENANT", "USER"]
OWNER_TYPE_CUSTOMER = "CUSTOMER"
LP_SERVER_ATTRIBUTES = ["accuracy", "lamp"]

DEVICE_FIND_QUERY_ATTRIBUTE_RESPONSE_KEYS = [
    "state", "condition", "region", "wardName", "zoneName", "location",
    "landmark", "latitude", "longitude", "slatitude", "slongitude",
    "commissioned", "lastActivityTime", "active", "lampWatts", "accuracy",
    "installedOn", "installedBy"
]

DEVICE_FIND_QUERY_ENTITY_RESPONSE_KEYS = {
    "name": "name", "label": "label", "type": "type", "device_id": "id"
}

PROJECT_WISE_WATTAGE = "lampWattage"

# Create settings instance
settings = Settings()

"""
Configuration settings for FastAPI application using Pydantic BaseSettings
"""
import os
from typing import Optional
from pydantic import BaseSettings
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class Settings(BaseSettings):
    """Application settings using Pydantic BaseSettings"""
    
    # Basic settings
    SECRET_KEY: str
    DEBUG: bool = False
    
    # Database settings
    DB_NAME: str
    DB_USER: str
    DB_PASSWORD: str
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    
    # ThingsBoard settings
    BASE_URL: str
    TB_USERNAME: str
    TB_PASSWORD: str
    
    # AWS settings
    AWS_BUCKET_TYPE: str
    AWS_BUCKET_NAME: str
    AWS_BUCKET_REGION: str
    AWS_BUCKET_ACCESS_KEY: str
    AWS_BUCKET_SECRET_KEY: str
    AWS_BUCKET_GRIEVANCE_FOLDER_NAME: str
    AWS_BUCKET_LUMINATOR_FOLDER_NAME: str
    AWS_BUCKET_PPE_IMAGE_FOLDER_NAME: str
    
    # GCP Integration settings
    SERVICE_ACCOUNT_KEY_PATH: str
    PUBSUB_POLE_TOPIC_PATH: str
    PUBSUB_LAMP_TOPIC_PATH: str
    PUBSUB_POLE_RELOCATION_TOPIC_PATH: str
    PUBSUB_ACTIVITY_LOG_TOPIC_PATH: str
    
    # AWS Install Service settings
    AWS_INSTALL_SERVICE_SQS_URL: str
    AWS_INSTALL_SERVICE_REGION: str
    AWS_INSTALL_SERVICE_ACCESS_KEY: str
    AWS_INSTALL_SERVICE_SECRET_KEY: str
    
    # Logging settings
    LOG_FILE_NAME: str
    LOG_LEVEL: str = "INFO"
    
    # GreyTHR settings
    GREYTHR_HOST: str
    GREYTHR_API_ID: str
    GREYTHR_API_ENDPOINT: str
    GREYTHR_PEM_FILEPATH: str
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()

# Constants from Django settings
HEADER = {'Content-type': 'application/json', 'Accept': '*/*'}

# CSML Data forwarding URLs
CSML_LOGIN_URL = "https://bi.smartkochi.in:8443/auth/realms/schnellenergy/protocol/openid-connect/token"
CSML_LAMP_FAILURE_URL = "https://bi.smartkochi.in:8443/apiman-gateway/FLUENTGRID/Lampfailure/1.0?apikey=1c7b2899-167f-443b-8b33-64885e6cac4a"
kochin_user_data = "username=SchnellEnergyUser&password=sch%24ell@n@r%26yu%24er&grant_type=password&client_id=SchnellEnergy"

# ThingsBoard constants
ILM_SERVER_ATTRIBUTES = "testResults,PDI,state,condition,dimmable,active,wardName,zoneName,region,qrCount"
LUME_PRINT_QR_REQUIRED_ROLE = "production manager"
ILM_ATTRIBUTE_TYPE_CONVERT_BOOL = ["dimmable", "PDI", "brightness_100", "brightness_70", "brightness_50",
                                   "brightness_30", "brightness_0", "flash", "rtc", "active"]
ILM_ATTRIBUTE_TYPE_CONVERT_INT = ["qrCount", "error_count", "start_ts", "end_ts", "lastActivityTime", "installedOn",
                                  "lampWatts", "armCount", "lampWattage"]
ILM_ATTRIBUTE_TYPE_CONVERT_DOUBLE = ["latitude", "longitude", "accuracy", "slatitude", "slongitude"]
GW_SERVER_ATTRIBUTES = "active,state,condition,wardName,zoneName,region"
ENTITY_TYPE = ["DEVICE", "ASSET"]
ENTITY_TYPE_ASSET = "ASSET"
ENTITY_TYPE_DEVICE = "DEVICE"
ATTRIBUTE_SCOPES = ["CLIENT_SCOPE", "SERVER_SCOPE", "SHARED_SCOPE"]
DEVICE_TYPES = ("gw", "ilm", "ilm-4g", "ccms", "nic", "smslc")
RELATION_TYPES = ["Contains", "ControlledBy", "LitBy", "Powers", "Routes", "Mounts", "CanAccess"]
ILM_TEST_QUERY_PARAMS = ["jigNumber", "deviceId", "action"]
GW_ENTITY_ID_KEY = ["deviceId"]
ENTITY_RELATIONS = ["FROM", "TO"]
GW_DISPATCH_QUERY_PARAMS = ["panelId", "gwType", "ebMeterNo", "phase", "simNo"]
GW_ASSET_TYPES = ("ccms", "hub", "smslc")
ILM_ASSET_TYPES = ["lamp", "lightPoint", "region", "zone", "ward", "pole", "switchPoint"]

# Installation attributes
LIGHT_POINT_INSTALLATION_ATTRIBUTES = ["latitude", "longitude", "accuracy", "landmark", "wardName", "zoneName",
                                       "region", "installedOn", "installedBy", "lampWatts"]
ILM_INSTALLATION_ATTRIBUTES = ["state", "condition", "landmark", "latitude", "longitude", "zoneName", "region",
                               "wardName", "installedOn", "installedBy"]
CCMS_INSTALLATION_ATTRIBUTES = ["slatitude", "slongitude", "accuracy", "location", "wardName", "zoneName", "region",
                                "installedOn", "installedBy"]
CCMS_EBMETER_ATTRIBUTES = {"ebMeterNo": "name", "meterReadingOffset": "meterReadingOffset",
                           "ebMeterReplacedBy": "replacedBy", "ebMeterReplacedOn": "replacedOn"}
GW_INSTALLATION_ATTRIBUTES = ["state", "condition", "location", "wardName", "zoneName", "region", "installedOn",
                              "installedBy"]

# More constants
POLE_INSTALLATION_ATTRIBUTES = ["type", "lampProfiles", "name", "discomPoleNumber", "height", "condition", "span", "clampDimension",
                                "vehicleAccessAvailable", "connection", "earthingRequired", "controlWireStatus", "armDetails", "accuracy",
                                "manualSwitchControl", "remarks", "signalStrength", "region", "zoneName", "sector", "wardName", "installedBy", "installedOn",
                                "roadCategory", "roadWidth", "incomingTransmissionLine", "incomingTransmissionType", "bracketMountingHeight",
                                "state", "locationDetails", "latitude", "longitude", "location", "armCount", "auditImg", "assetType", "roadType"]

LAMP_INSTALLATION_ATTRIBUTES = ["lampWatts", "manufacturer", "lampType", "year", "wardName", "installedOn",
                                "installedBy", "dimmable", "state", "condition"]

SWITCH_POINT_INSTALLATION_ATTRIBUTES = ["switchPointNumber", "switchPointType", "panelId", "rrNumber", "meter", "connectedLoad", "roadType", "assetType", 
                                        "workingCondition", "earthingCondition", "customerId", "wardId", "switchPointId", "remarks", "region",
                                        "zoneName", "wardName", "installedBy", "installedOn", "state", "locationDetails", "meterDetails", 
                                        "latitude", "longitude", "location", "accuracy", "roadCategory", "roadWidth", "vehicleAccess"]

DEVICE_FAILURE_STATUS_HANDLER = [200, 401]
COMMON_PARAMS = "lastActivityTime"
LIGHT_POINT_ASSET_FORMAT = "LP-{asset_name}"
LAMP_ASSET_FORMAT = "LMP-{asset_name}"
AVAILABLE_STATES = ["TESTABLE", "TESTED", "INSTALLABLE", "INSTALLED"]
AVAILABLE_CONDITION = ["NEW", "SERVICE", "SCRAPPED"]
REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION = {"state": "INSTALLABLE"}
LAMP_INSTALLED_STATE_CONDITION = {"state": "INSTALLED", "condition": "NEW"}
INSTALLED_STATE = {"state": "INSTALLED"}
OWNER_TYPE = ["CUSTOMER", "TENANT", "USER"]
OWNER_TYPE_CUSTOMER = "CUSTOMER"
LP_SERVER_ATTRIBUTES = ["accuracy", "lamp"]

DEVICE_FIND_QUERY_ATTRIBUTE_RESPONSE_KEYS = ["state", "condition", "region", "wardName", "zoneName", "location",
                                             "landmark", "latitude", "longitude", "slatitude", "slongitude",
                                             "commissioned", "lastActivityTime", "active", "lampWatts", "accuracy",
                                             "installedOn", "installedBy"]
DEVICE_FIND_QUERY_ENTITY_RESPONSE_KEYS = {"name": "name", "label": "label", "type": "type", "device_id": "id"}
PROJECT_WISE_WATTAGE = "lampWattage"

# AWS file path
AWS_FILE_PATH = f'http://{settings.AWS_BUCKET_NAME}.{settings.AWS_BUCKET_TYPE}.{settings.AWS_BUCKET_REGION}.amazonaws.com'

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://iotpro.io",
    "https://iotpro.io",
    "http://schnelliot.in",
    "https://schnelliot.in",
    "http://tbce.iotpro.io:8080",
    "http://prod.schnelliot.in",
    "https://prod.schnelliot.in",
    "http://prod.schnelliot.in:8080",
    "http://192.168.1.206:8080",
    "https://signify.iotpro.io",
    "https://customergrievance-9acd9.web.app"
]

# Request session with retry configuration
session = requests.Session()
retry = Retry(total=5, connect=5, backoff_factor=0.5, status_forcelist=[500, 502, 503, 504])
adapter = HTTPAdapter(max_retries=retry, pool_maxsize=100, pool_connections=100)
session.mount('http://', adapter)
session.mount('https://', adapter)

# Data upload limit
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10 MB

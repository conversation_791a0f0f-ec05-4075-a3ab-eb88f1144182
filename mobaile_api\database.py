"""
Database configuration for FastAPI
Using SQLAlchemy with MySQL
"""

import pymysql
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from .config import settings

# Install PyMySQL as MySQLdb
pymysql.install_as_MySQLdb()

# Database URL
DATABASE_URL = f"mysql+pymysql://{settings.db_user}:{settings.db_password}@{settings.db_host}:{settings.db_port}/{settings.db_name}"

# SQLAlchemy setup
engine = create_engine(DATABASE_URL, echo=settings.debug)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
metadata = MetaData()

# Database dependency for FastAPI
def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# PyMySQL connection configuration (for legacy code compatibility)
db_config = {
    'host': settings.db_host,
    'user': settings.db_user,
    'password': settings.db_password,
    'db': settings.db_name,
    'port': settings.db_port,
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
}

@contextmanager
def get_pymysql_connection():
    """Context manager for PyMySQL connections (for legacy code)"""
    conn = pymysql.connect(**db_config)
    try:
        yield conn
    finally:
        conn.close()

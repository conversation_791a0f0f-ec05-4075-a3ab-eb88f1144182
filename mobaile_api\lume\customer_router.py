"""
FastAPI router for Customer-related endpoints
"""
import json
import logging
from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import JSONResponse

from .dependencies import (
    RequestResponseHandler, require_token, get_request_handler
)
from .service import EntitySearchService
from .schemas import StandardResponse
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/customers/", response_model=StandardResponse)
async def get_customers(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get customers endpoint"""
    try:
        search_service = EntitySearchService(token=token, instance=request_handler)
        customers = search_service.get_entities(entity_type="customer")
        request_handler.update_service_response(customers)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to get customers: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.get("/regions/", response_model=StandardResponse)
async def get_regions(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get regions endpoint"""
    try:
        search_service = EntitySearchService(token=token, instance=request_handler)
        
        # Search for region assets
        search_params = {
            "entityTypes": ["ASSET"],
            "assetTypes": ["region"],
            "pageSize": 1000,
            "page": 0
        }
        
        regions = search_service.search_entities(search_params)
        request_handler.update_service_response(regions)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to get regions: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.get("/zones/", response_model=StandardResponse)
async def get_zones(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get zones endpoint"""
    try:
        search_service = EntitySearchService(token=token, instance=request_handler)
        
        # Search for zone assets
        search_params = {
            "entityTypes": ["ASSET"],
            "assetTypes": ["zone"],
            "pageSize": 1000,
            "page": 0
        }
        
        zones = search_service.search_entities(search_params)
        request_handler.update_service_response(zones)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to get zones: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.get("/wards/", response_model=StandardResponse)
async def get_wards(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get wards endpoint"""
    try:
        search_service = EntitySearchService(token=token, instance=request_handler)
        
        # Search for ward assets
        search_params = {
            "entityTypes": ["ASSET"],
            "assetTypes": ["ward"],
            "pageSize": 1000,
            "page": 0
        }
        
        wards = search_service.search_entities(search_params)
        request_handler.update_service_response(wards)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to get wards: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.get("/get/assets/", response_model=StandardResponse)
async def get_asset_lat_lon(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get assets with latitude and longitude"""
    try:
        search_service = EntitySearchService(token=token, instance=request_handler)
        
        # Get query parameters
        asset_type = request.query_params.get('assetType', 'lightPoint')
        page_size = int(request.query_params.get('pageSize', 100))
        page = int(request.query_params.get('page', 0))
        
        # Search for assets with location data
        search_params = {
            "entityTypes": ["ASSET"],
            "assetTypes": [asset_type],
            "pageSize": page_size,
            "page": page
        }
        
        assets = search_service.search_entities(search_params)
        
        # Filter assets that have latitude and longitude
        if assets.get("status") == 200 and "data" in assets:
            filtered_assets = []
            for asset in assets["data"]:
                # Check if asset has location attributes
                # This is a simplified version - you may need to fetch attributes separately
                filtered_assets.append(asset)
            
            assets["data"] = filtered_assets
        
        request_handler.update_service_response(assets)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to get assets with lat/lon: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.get("/get/project/wattage/", response_model=StandardResponse)
async def get_project_wise_wattage(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get project wise wattage"""
    try:
        project_id = request.query_params.get('projectId')
        
        if not project_id:
            raise HTTPException(status_code=400, detail={"status": 400, "message": "projectId is required"})
        
        # Get project wise wattage
        result = UtilService.get_project_wise_wattage(project_id)
        request_handler.update_service_response(result)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to get project wise wattage: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)

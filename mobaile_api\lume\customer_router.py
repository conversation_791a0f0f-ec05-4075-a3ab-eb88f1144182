"""
Lume Customer Router - Customer and location related APIs
Converted from Django lume/views.py
"""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query

from .schemas import StandardResponse
from .dependencies import RequestResponseHandler, get_request_handler, validate_token
from .service import AssetService, CustomerService
from ..config import settings, ENTITY_TYPE, ILM_ASSET_TYPES, RELATION_TYPES

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["Customer & Location APIs"])


@router.get("/customers/", response_model=StandardResponse)
async def get_customers(
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Get customers
    Converted from Django lume.views.get_customers
    """
    try:
        customer_service = CustomerService(token, request_handler)
        # Implement customer retrieval logic here
        
        customers = {"data": []}
        request_handler.update_service_response(customers)
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to get customers: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/regions/", response_model=StandardResponse)
async def get_regions(
    customerId: Optional[str] = Query(None, description="Customer ID"),
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Get regions
    Converted from Django lume.views.get_regions
    """
    try:
        if customerId:
            asset_service = AssetService(token, request_handler)
            region_query = asset_service.asset_lat_lon(
                entity_id=str(customerId), 
                entity_type=ENTITY_TYPE[0],  # DEVICE
                asset_type=[ILM_ASSET_TYPES[2]],  # region
                relation_type=RELATION_TYPES[0]  # Contains
            )
            region_details = asset_service.asset_related_asset(query=region_query)
            request_handler.update_service_response(
                asset_service.construct_asset_details(
                    data=region_details.get("data"),
                    asset_type="regions"
                )
            )
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
        
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to get regions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/zones/", response_model=StandardResponse)
async def get_zones(
    regionId: Optional[str] = Query(None, description="Region ID"),
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Get zones
    Converted from Django lume.views.get_zones
    """
    try:
        if regionId:
            asset_service = AssetService(token, request_handler)
            zone_query = asset_service.asset_lat_lon(
                entity_id=str(regionId), 
                entity_type=ENTITY_TYPE[1],  # ASSET
                asset_type=[ILM_ASSET_TYPES[3]],  # zone
                relation_type=RELATION_TYPES[0]  # Contains
            )
            zone_details = asset_service.asset_related_asset(query=zone_query)
            request_handler.update_service_response(
                asset_service.construct_asset_details(
                    data=zone_details.get("data"),
                    asset_type="zones"
                )
            )
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
        
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to get zones: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/wards/", response_model=StandardResponse)
async def get_wards(
    zoneId: Optional[str] = Query(None, description="Zone ID"),
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Get wards
    Converted from Django lume.views.get_wards
    """
    try:
        if zoneId:
            asset_service = AssetService(token, request_handler)
            ward_query = asset_service.asset_lat_lon(
                entity_id=str(zoneId), 
                entity_type=ENTITY_TYPE[1],  # ASSET
                asset_type=[ILM_ASSET_TYPES[4]],  # ward
                relation_type=RELATION_TYPES[0]  # Contains
            )
            ward_details = asset_service.asset_related_asset(query=ward_query)
            request_handler.update_service_response(
                asset_service.construct_asset_details(
                    data=ward_details.get("data"),
                    asset_type="wards"
                )
            )
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
        
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to get wards: {e}")
        raise HTTPException(status_code=500, detail=str(e))

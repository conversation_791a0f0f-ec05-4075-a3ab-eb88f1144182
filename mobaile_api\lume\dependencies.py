"""
FastAPI Dependencies and Handlers
Converted from Django lume/handler.py
"""

import json
import logging
from functools import wraps
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, Header, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

logger = logging.getLogger(__name__)

# Security scheme for token authentication
security = HTTPBearer()


class RequestResponseHandler:
    """
    Request Response Handler
    Converted from Django handler
    """
    
    def __init__(self):
        self.service_response = {"status": 200, "message": "Success"}
        self.error_response = {"status": 500, "message": "Internal Server Error"}
    
    def update_service_response(self, data: Any) -> None:
        """Update service response with data"""
        if isinstance(data, dict) and "status" in data:
            self.service_response.update(data)
        else:
            self.service_response["data"] = data
    
    def update_error_response(self, message: str, status: int = 500) -> None:
        """Update error response with message"""
        self.error_response = {"status": status, "message": message}
        raise HTTPException(status_code=status, detail=message)
    
    def get_service_response(self) -> Dict[str, Any]:
        """Get service response"""
        return self.service_response
    
    def get_error_response(self) -> Dict[str, Any]:
        """Get error response"""
        return self.error_response
    
    @staticmethod
    def post_request(url: str, header: Dict[str, str], data: str, instance: 'RequestResponseHandler') -> Dict[str, Any]:
        """Make POST request"""
        import requests
        try:
            response = requests.post(url=url, headers=header, data=data, timeout=60)
            if response.status_code == 200:
                return {"status": 200, "data": response.json()}
            else:
                return {"status": response.status_code, "message": response.text}
        except Exception as e:
            logger.exception(f"POST request failed: {e}")
            return {"status": 500, "message": str(e)}
    
    @staticmethod
    def get_request(url: str, header: Dict[str, str], data: str, instance: 'RequestResponseHandler') -> Dict[str, Any]:
        """Make GET request"""
        import requests
        try:
            response = requests.get(url=url, headers=header, timeout=60)
            if response.status_code == 200:
                return {"status": 200, "data": response.json()}
            else:
                return {"status": response.status_code, "message": response.text}
        except Exception as e:
            logger.exception(f"GET request failed: {e}")
            return {"status": 500, "message": str(e)}


async def get_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """
    Extract and validate token from Authorization header
    Converted from Django token_validator decorator
    """
    if not credentials or not credentials.credentials:
        raise HTTPException(status_code=401, detail="Token not found")
    return credentials.credentials


async def get_token_optional(authorization: Optional[str] = Header(None)) -> Optional[str]:
    """
    Extract token from Authorization header (optional)
    """
    if authorization and authorization.startswith("Bearer "):
        return authorization.split(" ")[1]
    return None


def validate_method(allowed_methods: List[str]):
    """
    Method validation decorator
    Converted from Django method_validate decorator
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            if request.method not in allowed_methods:
                raise HTTPException(status_code=405, detail=f"Method {request.method} not allowed")
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator


def validate_keys(required_keys: List[str]):
    """
    Key validation decorator
    Converted from Django validate_keys decorator
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            try:
                body = await request.body()
                request_params = json.loads(body)
                missing_keys = [key for key in required_keys if key not in request_params]
                if missing_keys:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Missing keys: {', '.join(missing_keys)}"
                    )
                return await func(request, *args, **kwargs)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid JSON in request body")
        return wrapper
    return decorator


async def get_request_handler() -> RequestResponseHandler:
    """
    Dependency to get RequestResponseHandler instance
    """
    return RequestResponseHandler()


# Token validation dependency
async def validate_token(token: str = Depends(get_token)) -> str:
    """
    Validate token dependency
    """
    # Add your token validation logic here
    # For now, just return the token
    return token


# Optional token validation dependency
async def validate_token_optional(token: Optional[str] = Depends(get_token_optional)) -> Optional[str]:
    """
    Optional token validation dependency
    """
    return token

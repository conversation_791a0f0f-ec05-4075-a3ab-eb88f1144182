"""
FastAPI dependencies for authentication and validation
"""
import json
from typing import Op<PERSON>
from fastapi import HTTP<PERSON>x<PERSON>, <PERSON><PERSON>, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging

from ..config import settings

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)


class RequestResponseHandler:
    """Request response handler for maintaining compatibility with Django version"""
    
    def __init__(self):
        self.response = {"status": 200}

    def update_service_response(self, response) -> None:
        self.response = response

    def get_service_response(self) -> dict:
        return self.response

    def update_error_response(self, error: str) -> None:
        previous_response = self.get_service_response()
        if previous_response.get("status") == 200:
            self.update_service_response({"status": 500, "message": error})


async def get_token_from_header(
    authorization: Optional[HTTPAuthorizationCredentials] = Depends(security),
    token: Optional[str] = Header(None)
) -> Optional[str]:
    """
    Extract token from Authorization header or token header
    """
    if authorization:
        return authorization.credentials
    elif token:
        return token
    return None


async def require_token(
    token: Optional[str] = Depends(get_token_from_header)
) -> str:
    """
    Dependency that requires a valid token
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"status": 1000, "message": "Token not found"}
        )
    return token


async def validate_method(request: Request, allowed_methods: list):
    """
    Validate HTTP method
    """
    if request.method not in allowed_methods:
        raise HTTPException(
            status_code=405,
            detail={"status": 405, "message": "The request method is not allowed"}
        )


def validate_required_keys(required_keys: list):
    """
    Decorator to validate required keys in request body
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Get request from kwargs
            request = None
            for arg in args:
                if hasattr(arg, 'method'):
                    request = arg
                    break
            
            if request:
                try:
                    body = await request.body()
                    if body:
                        request_params = json.loads(body)
                        missing_keys = [key for key in required_keys if key not in request_params]
                        if missing_keys:
                            raise HTTPException(
                                status_code=400,
                                detail={"status": 1000, "message": f"Missing keys {', '.join(missing_keys)}"}
                            )
                except json.JSONDecodeError:
                    raise HTTPException(
                        status_code=400,
                        detail={"status": 1000, "message": "Invalid JSON in request body"}
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


async def get_request_handler() -> RequestResponseHandler:
    """
    Dependency to get request handler instance
    """
    return RequestResponseHandler()


class TokenValidator:
    """
    Token validator class for maintaining compatibility
    """
    
    @staticmethod
    def validate_token(token: str) -> bool:
        """
        Validate token (placeholder - implement actual validation)
        """
        # TODO: Implement actual token validation logic
        return token is not None and len(token) > 0


def method_validate(required_method: str):
    """
    Method validation decorator for compatibility with Django version
    """
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            if request.method != required_method:
                raise HTTPException(
                    status_code=405,
                    detail={"status": 405, "message": "The request method is not allowed"}
                )
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator


def token_validator(func):
    """
    Token validation decorator for compatibility with Django version
    """
    async def wrapper(request: Request, *args, **kwargs):
        token = request.headers.get("token") or request.headers.get("authorization")
        if token and token.startswith("Bearer "):
            token = token[7:]
        
        if not token:
            raise HTTPException(
                status_code=401,
                detail={"status": 1000, "message": "Token not found"}
            )
        
        # Add token to request for downstream use
        request.state.token = token
        return await func(request, *args, **kwargs)
    return wrapper

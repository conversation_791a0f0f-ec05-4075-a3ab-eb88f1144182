"""
FastAPI router for Device-related endpoints
"""
import json
import logging
import time
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, HTTPException, Depends
from fastapi.responses import JSONResponse

from .dependencies import (
    RequestResponseHandler, require_token, get_request_handler
)
from .service import (
    DeviceService, AssetService, EntitySearchService, UtilService, GreyTHRService
)
from .schemas import StandardResponse
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/entities/search/", response_model=StandardResponse)
async def entities_search(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Search entities endpoint"""
    try:
        body = await request.body()
        search_params = json.loads(body)
        
        search_service = EntitySearchService(token=token, instance=request_handler)
        result = search_service.search_entities(search_params)
        request_handler.update_service_response(result)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to search entities: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/entity/detail/", response_model=StandardResponse)
async def entity_detail(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get entity detail endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)
        
        entity_id = request_params.get('entityId')
        entity_type = request_params.get('entityType', 'DEVICE')
        
        if not entity_id:
            raise HTTPException(status_code=400, detail={"status": 400, "message": "entityId is required"})
        
        search_service = EntitySearchService(token=token, instance=request_handler)
        result = search_service.get_entity_detail(entity_id, entity_type)
        request_handler.update_service_response(result)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to get entity detail: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.get("/entities/", response_model=StandardResponse)
async def get_entities(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get entities endpoint"""
    try:
        entity_type = request.query_params.get('entityType', 'device')
        page_size = int(request.query_params.get('pageSize', 100))
        page = int(request.query_params.get('page', 0))
        
        search_service = EntitySearchService(token=token, instance=request_handler)
        result = search_service.get_entities(entity_type, page_size, page)
        request_handler.update_service_response(result)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to get entities: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/gw/dispatch/", response_model=StandardResponse)
async def dispatch_gw(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Gateway dispatch endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)
        
        device_service = DeviceService(token=token, instance=request_handler)
        device_id = request_params.get(settings.ILM_TEST_QUERY_PARAMS[1])
        
        get_device_name = device_service.get_entity_by_id(
            entity_type=settings.ENTITY_TYPE[0],
            entity_id=device_id
        )
        device_name = get_device_name.get("name")
        
        device_attributes = device_service.construct_attributes(
            request_attributes=request_params,
            invalid_keys=settings.GW_DISPATCH_QUERY_PARAMS[0:3] + settings.GW_ENTITY_ID_KEY
        )
        
        device_service.get_and_remove_to_relations(
            to_id=device_id,
            to_type=settings.ENTITY_TYPE[0],
            removal_type=settings.ENTITY_RELATIONS[0].lower(),
            removal=True
        )
        
        if request_params.get(settings.GW_DISPATCH_QUERY_PARAMS[1], "").lower() == "gw":
            device_service.update_entity_attribute(
                entity_type=settings.ENTITY_TYPE[0],
                entity_id=device_id,
                scope=settings.ATTRIBUTE_SCOPES[1],
                data=json.dumps(device_attributes)
            )
        else:
            # Handle asset creation for CCMS/Hub
            asset_service = AssetService(token=token, instance=request_handler)
            
            if request_params.get(settings.GW_DISPATCH_QUERY_PARAMS[1], "").lower() == settings.GW_ASSET_TYPES[1]:
                asset_attributes = device_service.construct_attributes(
                    request_attributes=request_params,
                    invalid_keys=settings.GW_DISPATCH_QUERY_PARAMS[0:4] + settings.GW_ENTITY_ID_KEY
                )
            else:
                asset_attributes = device_service.construct_attributes(
                    request_attributes=request_params,
                    invalid_keys=settings.GW_DISPATCH_QUERY_PARAMS[0:2] + settings.GW_ENTITY_ID_KEY
                )
            
            new_asset_detail = asset_service.construct_asset(
                request_param=request_params,
                label=device_name
            )
            
            if new_asset_detail:
                asset_detail = asset_service.create_or_update_entity(
                    entity_type=settings.ENTITY_TYPE[1],
                    data=new_asset_detail
                )
                
                updated_ccms_detail = asset_service.validate_and_update_ccms(
                    asset_detail=asset_detail,
                    device_name=device_name
                )
                
                panel_id = updated_ccms_detail.get("id")
                ccms_name = updated_ccms_detail.get("name")
                
                relation_detail = asset_service.create_entity_relation(
                    from_id=panel_id,
                    from_type=settings.ENTITY_TYPE[1],
                    to_id=device_id,
                    to_type=settings.ENTITY_TYPE[0],
                    relation_type=settings.RELATION_TYPES[1]
                )
                
                uid_attribute = {"uid": ccms_name, "baselineOnTime": 43200}
                get_device_name["label"] = ccms_name
                
                device_service.create_device(
                    entity_type=settings.ENTITY_TYPE[0],
                    data=get_device_name
                )
                
                asset_attributes.update(uid_attribute)
                
                if relation_detail.get('status') == 200:
                    device_service.update_entity_attribute(
                        entity_type=settings.ENTITY_TYPE[1],
                        entity_id=panel_id,
                        scope=settings.ATTRIBUTE_SCOPES[1],
                        data=json.dumps(asset_attributes)
                    )
                    device_service.update_entity_attribute(
                        entity_type=settings.ENTITY_TYPE[0],
                        entity_id=device_id,
                        scope=settings.ATTRIBUTE_SCOPES[1],
                        data=json.dumps(device_attributes)
                    )
            else:
                request_handler.update_service_response({
                    "status": 1000,
                    "message": "Error While Create CCMS/GW Asset"
                })
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Couldn't dispatch gw device with asset: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/gw/install/", response_model=StandardResponse)
async def gw_install(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Gateway install endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)
        
        device_service = DeviceService(token=token, instance=request_handler)
        device_id = request_params.get('deviceId')
        
        if not device_id:
            raise HTTPException(status_code=400, detail={"status": 400, "message": "deviceId is required"})
        
        # Construct installation attributes
        install_attributes = device_service.construct_attributes(
            request_attributes=request_params,
            invalid_keys=['deviceId']
        )
        
        # Update device attributes
        device_service.update_entity_attribute(
            entity_type=settings.ENTITY_TYPE[0],
            entity_id=device_id,
            scope=settings.ATTRIBUTE_SCOPES[1],
            data=json.dumps(install_attributes)
        )
        
        request_handler.update_service_response({
            "status": 200,
            "message": "Gateway installed successfully"
        })
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Gateway installation failed: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/gw/update/", response_model=StandardResponse)
async def update_gw_attribute_details(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Update gateway attribute details endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)
        
        device_service = DeviceService(token=token, instance=request_handler)
        device_id = request_params.get(settings.ILM_TEST_QUERY_PARAMS[1])
        
        get_attributes = device_service.construct_attributes(
            request_attributes=request_params,
            invalid_keys=settings.GW_DISPATCH_QUERY_PARAMS + settings.GW_ENTITY_ID_KEY
        )
        
        if request_params.get("state", "").lower() == settings.AVAILABLE_STATES[0].lower():
            device_service.get_and_remove_to_relations(
                to_id=device_id,
                to_type=settings.ENTITY_TYPE[0],
                removal_type=settings.ENTITY_RELATIONS[0].lower(),
                removal=True
            )
        
        if request_params.get("condition", "").lower() == settings.AVAILABLE_CONDITION[2].lower():
            device_service.get_and_remove_from_and_to_relations(
                entity_id=device_id,
                entity_type=settings.ENTITY_TYPE[0]
            )
        
        device_service.update_entity_attribute(
            entity_type=settings.ENTITY_TYPE[0],
            entity_id=device_id,
            scope=settings.ATTRIBUTE_SCOPES[1],
            data=json.dumps(get_attributes)
        )
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Couldn't update gw device attributes: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/gw/control/", response_model=StandardResponse)
async def gw_control(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Gateway control endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)
        
        # Implement gateway control logic here
        # This is a placeholder for the actual control implementation
        request_handler.update_service_response({
            "status": 200,
            "message": "Gateway control command sent successfully"
        })
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Gateway control failed: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)

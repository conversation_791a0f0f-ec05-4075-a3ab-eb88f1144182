"""
Lume Router - Main FastAPI routes for Luminator APIs
Converted from Django lume/views.py
"""

import json
import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request, Form, Query
from fastapi.responses import JSONResponse

from .schemas import (
    LoginRequest, LoginResponse, DeviceDetailsRequest, DeviceServiceRequest,
    EntitySearchRequest, EntityDetailRequest, AssetLatLonRequest,
    StandardResponse
)
from .dependencies import (
    RequestResponseHandler, get_request_handler, validate_token, 
    validate_token_optional
)
from .service import (
    TbAuthService, AttendanceService, EntityService, AssetService,
    DeviceService, CustomerService, LampService
)
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["Lume APIs"])


@router.post("/login/", response_model=StandardResponse)
async def login(
    username: str = Form(...),
    password: str = Form(...),
    ts: Optional[str] = Form(None),
    latitude: Optional[str] = Form(None),
    longitude: Optional[str] = Form(None),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    User login endpoint
    Converted from Django lume.views.login
    """
    try:
        logger.info(f"User Login: {username}")
        if not username or not password:
            raise HTTPException(status_code=400, detail="Username/Password is required.")
        
        attendance_service = AttendanceService()
        attendance_service.handle_login(
            _username=username, 
            _password=password,
            request_handler=request_handler, 
            _ts=ts,
            latitude=latitude, 
            longitude=longitude
        )
        
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to login: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/print_qr/", response_model=StandardResponse)
async def get_current_user_role(
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Get current user role for QR printing
    Converted from Django lume.views.get_current_user_role
    """
    try:
        tb_auth_service = TbAuthService(request_handler)
        user_permissions = tb_auth_service.get_user_permissions(token)
        
        if user_permissions.get("status") == 200:
            role_availability = TbAuthService.get_roles(user_permissions.get("data", []))
            request_handler.update_service_response({
                "status": 200,
                "canPrintQR": role_availability
            })
        else:
            request_handler.update_error_response("Failed to get user permissions")
        
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to get user role: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get/device/", response_model=StandardResponse)
async def get_device_details(
    deviceId: str = Query(..., description="Device ID"),
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Get device details
    Converted from Django lume.views.get_device_details
    """
    try:
        entity_service = EntityService(token, request_handler)
        device_info = entity_service.get_entity_info_by_id("device", deviceId)
        
        if device_info and device_info.get("status") == 200:
            request_handler.update_service_response(device_info.get("data"))
        else:
            request_handler.update_error_response("Device not found", 404)
        
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to get device details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/device/service/", response_model=StandardResponse)
async def device_take_service(
    request: Request,
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Device take service
    Converted from Django lume.views.device_take_service
    """
    try:
        body = await request.body()
        request_params = json.loads(body)
        
        device_id = request_params.get("deviceId")
        if not device_id:
            raise HTTPException(status_code=400, detail="deviceId is required")
        
        device_service = DeviceService(token, request_handler)
        # Implement device service logic here
        
        request_handler.update_service_response({"status": 200, "message": "Service completed"})
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to service device: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/entities/search/", response_model=StandardResponse)
async def entities_search(
    entityType: str = Query(..., description="Entity type"),
    searchText: Optional[str] = Query(None, description="Search text"),
    pageSize: Optional[int] = Query(20, description="Page size"),
    page: Optional[int] = Query(0, description="Page number"),
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Search entities
    Converted from Django lume.views.entities_search
    """
    try:
        entity_service = EntityService(token, request_handler)
        # Implement entity search logic here
        
        search_results = {
            "data": [],
            "totalElements": 0,
            "totalPages": 0
        }
        
        request_handler.update_service_response(search_results)
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to search entities: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/entity/detail/", response_model=StandardResponse)
async def entity_detail(
    entityId: str = Query(..., description="Entity ID"),
    entityType: str = Query(..., description="Entity type"),
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Get entity details
    Converted from Django lume.views.entity_detail
    """
    try:
        entity_service = EntityService(token, request_handler)
        entity_info = entity_service.get_entity_info_by_id(entityType, entityId)
        
        if entity_info and entity_info.get("status") == 200:
            request_handler.update_service_response(entity_info.get("data"))
        else:
            request_handler.update_error_response("Entity not found", 404)
        
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to get entity details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/entities/", response_model=StandardResponse)
async def get_entities(
    entityType: Optional[str] = Query(None, description="Entity type"),
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Get entities
    Converted from Django lume.views.get_entities
    """
    try:
        entity_service = EntityService(token, request_handler)
        # Implement get entities logic here
        
        entities = {"data": []}
        request_handler.update_service_response(entities)
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to get entities: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get/assets/", response_model=StandardResponse)
async def get_asset_lat_lon(
    assetType: Optional[str] = Query(None, description="Asset type"),
    customerId: Optional[str] = Query(None, description="Customer ID"),
    token: str = Depends(validate_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """
    Get asset latitude longitude
    Converted from Django lume.views.get_asset_lat_lon
    """
    try:
        asset_service = AssetService(token, request_handler)
        # Implement asset lat/lon logic here
        
        assets = {"data": []}
        request_handler.update_service_response(assets)
        return request_handler.get_service_response()
        
    except Exception as e:
        logger.exception(f"Failed to get asset lat/lon: {e}")
        raise HTTPException(status_code=500, detail=str(e))

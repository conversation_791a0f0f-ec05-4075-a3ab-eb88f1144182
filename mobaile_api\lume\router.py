"""
FastAPI router for Lume module - Main API endpoints
"""
import json
import logging
from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, Depends, Form
from fastapi.responses import JSONResponse

from .schemas import (
    LoginRequest, LoginResponse, LogoutRequest, DeviceRequest, DeviceResponse,
    ILMTestRequest, ILMUpdateRequest, GWDispatchRequest, GWInstallRequest,
    GWControlRequest, ILMControlRequest, EBMeterReplaceRequest, PoleInstallRequest,
    LuminaireMaintainRequest, ImageUploadRequest, TicketSerializerRequest,
    LightPointCommissionRequest, LocationUpdateRequest, PPEImageUploadRequest,
    AttendanceSwipesRequest, SwitchPointInstallRequest, IAMUserRequest,
    StandardResponse
)
from .dependencies import (
    RequestResponseHandler, require_token, get_request_handler,
    token_validator, method_validate
)
from .service import (
    TbAuthService, DeviceService, AssetService, AttendanceService,
    UtilService, GreyTHRService, EntitySearchService
)
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/login/", response_model=StandardResponse)
async def login(request: Request):
    """User login endpoint"""
    request_handler = RequestResponseHandler()
    try:
        # Get form data
        form_data = await request.form()
        username = form_data.get('username')
        password = form_data.get('password')
        ts = form_data.get('ts')
        latitude = form_data.get('latitude')
        longitude = form_data.get('longitude')
        
        logger.info(f"User Login: {username}")
        
        if not username or not password:
            raise HTTPException(status_code=400, detail={"status": 400, "message": "Username/Password is required."})
        
        attendance_service = AttendanceService()
        attendance_service.handle_login(
            _username=username,
            _password=password,
            request_handler=request_handler,
            _ts=ts,
            latitude=latitude,
            longitude=longitude
        )
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to login: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/iam_user/", response_model=StandardResponse)
async def add_iam_user(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Add IAM user endpoint"""
    try:
        form_data = await request.form()
        customer_name = form_data.get('customer_name')
        region = form_data.get('region')
        user_email_id = form_data.get('user_email_id')
        
        if not customer_name:
            raise HTTPException(status_code=400, detail={"status": 400, "message": "customer_name is required."})
        if not region:
            raise HTTPException(status_code=400, detail={"status": 400, "message": "region is required."})
        if not user_email_id:
            raise HTTPException(status_code=400, detail={"status": 400, "message": "user_email_id is required."})

        attendance_service = AttendanceService()
        attendance_service.publish_user_data_to_pubsub(
            customer_name=customer_name,
            region=region,
            user_email_id=user_email_id
        )
        
        request_handler.update_service_response({"status": 200, "message": "IAM user data published successfully"})
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to add IAM user: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/print_qr/", response_model=StandardResponse)
async def get_current_user_role(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get current user role for QR printing"""
    try:
        auth_service = TbAuthService(instance=request_handler)
        roles = auth_service.get_user_permissions(header=token)
        has_role = auth_service.get_roles(roles)
        
        response = {"status": 200 if has_role else 404}
        return JSONResponse(content=response)
        
    except Exception as e:
        logger.exception(f"Couldn't get user role: {e}")
        return JSONResponse(content={"status": 500, "message": "Internal server error"})


@router.post("/logout/", response_model=StandardResponse)
async def logout(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """User logout endpoint"""
    try:
        form_data = await request.form()
        ts = form_data.get('ts')
        latitude = form_data.get('latitude')
        longitude = form_data.get('longitude')
        
        auth_service = TbAuthService(instance=request_handler)
        user_info = auth_service.get_user_details(header=token)
        username = user_info.get("name")
        
        attendance_service = AttendanceService()
        attendance_service.handle_logout(
            _token=token,
            request_handler=request_handler,
            _username=username,
            _ts=ts,
            latitude=latitude,
            longitude=longitude
        )
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Failed to logout: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/get/device/", response_model=StandardResponse)
async def get_device_details(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Get device details endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)
        
        device_service = DeviceService(token=token, instance=request_handler)
        device_query_param = request_params.get('device')
        device_details = device_service.get_entity_by_name(entity_type="device", name=device_query_param)
        device_id = device_details.get('id').get('id')
        
        get_relation_info = []
        attribute_search_keys = f"{settings.GW_SERVER_ATTRIBUTES},{settings.COMMON_PARAMS}"
        device_profile = device_details.get("type").lower()
        
        if device_profile in (settings.DEVICE_TYPES[1], settings.DEVICE_TYPES[2], settings.DEVICE_TYPES[4]):
            get_relation_info = device_service.get_entity_to_relation_info(
                to_id=device_id,
                to_type=settings.ENTITY_TYPE[0]
            )
            attribute_search_keys = f"{settings.ILM_SERVER_ATTRIBUTES},{settings.COMMON_PARAMS}"
        elif device_profile in (settings.DEVICE_TYPES[0], settings.DEVICE_TYPES[3], settings.DEVICE_TYPES[4], settings.DEVICE_TYPES[5]):
            to_relation_query = device_service.gw_device_relations(
                entity_id=device_id,
                entity_type=settings.ENTITY_TYPE[0],
                direction=settings.ENTITY_RELATIONS[1],
                relation_type=settings.RELATION_TYPES[1]
            )
            to_relation_detail = device_service.find_entities_by_query(query=to_relation_query)
            get_relation_info = device_service.relation_construct(
                data=to_relation_detail.get("data", []),
                attributes=True
            )
        
        get_attributes = device_service.get_entity_attribute(
            entity_type=settings.ENTITY_TYPE[0],
            entity_id=device_id,
            scope=settings.ATTRIBUTE_SCOPES[1],
            keys=attribute_search_keys
        )
        
        response_data = device_service.get_lume_ilm_packet(
            device_details, get_attributes, get_relation_info, device_details.get('type')
        )
        request_handler.update_service_response(response_data)
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Couldn't get device details: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/device/service/", response_model=StandardResponse)
async def device_take_service(
    request: Request,
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Device take service endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)
        
        token = request.headers.get('token') or request.headers.get('authorization', '').replace('Bearer ', '')
        device_service = DeviceService(token=token, instance=request_handler)
        
        device_id = request_params.get(settings.ILM_TEST_QUERY_PARAMS[1])
        req_condition = request_params.get(settings.GW_INSTALLATION_ATTRIBUTES[1])
        
        get_attributes = device_service.construct_attributes(
            request_attributes=request_params,
            invalid_keys=settings.GW_DISPATCH_QUERY_PARAMS + settings.GW_ENTITY_ID_KEY
        )
        
        if req_condition == settings.AVAILABLE_CONDITION[1]:
            device_service.get_and_remove_from_and_to_relations(
                entity_id=device_id,
                entity_type=settings.ENTITY_TYPE[0]
            )
            device_service.update_entity_attribute(
                entity_type=settings.ENTITY_TYPE[0],
                entity_id=device_id,
                scope=settings.ATTRIBUTE_SCOPES[1],
                data=json.dumps(get_attributes)
            )
        
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Device service operation failed: {e}")
    
    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/ilm/test/", response_model=StandardResponse)
async def ilm_test_initiate_and_reset(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """ILM test initiate and reset endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)

        device_service = DeviceService(token=token, instance=request_handler)
        attribute_json = device_service.ilm_test_attributes()
        device_id = request_params.get(settings.ILM_TEST_QUERY_PARAMS[1])

        get_test_details = device_service.validate_test_initiate(request_params, attribute_json)

        if get_test_details:
            jig_name = request_params.get(settings.ILM_TEST_QUERY_PARAMS[0])
            asset_service = AssetService(token=token, instance=request_handler)
            asset_details = asset_service.get_entity_by_name(
                entity_type=settings.ENTITY_TYPE[1],
                name=jig_name
            )

            if asset_details.get("type") == settings.ILM_ASSET_TYPES[1]:
                asset_from_relation_details = asset_service.get_entity_from_relation(
                    from_id=asset_details.get("id").get("id"),
                    from_type=settings.ENTITY_TYPE[1]
                )
                asset_service.remove_asset_relations(
                    asset_relations=asset_from_relation_details,
                    related_id=asset_details.get("id").get("id"),
                    related_type=settings.ENTITY_TYPE[1],
                    removal_type=settings.ENTITY_RELATIONS[1].lower()
                )
                device_service.get_and_remove_to_relations(
                    to_id=device_id,
                    to_type=settings.ENTITY_TYPE[0],
                    removal_type=settings.ENTITY_RELATIONS[0].lower(),
                    removal=True
                )
                asset_service.create_entity_relation(
                    from_id=asset_details.get("id").get("id"),
                    from_type=settings.ENTITY_TYPE[1],
                    to_id=device_id,
                    to_type=settings.ENTITY_TYPE[0],
                    relation_type=settings.RELATION_TYPES[1]
                )
                get_test_details["testResults"]["jig_number"] = jig_name
                device_service.update_entity_attribute(
                    entity_type=settings.ENTITY_TYPE[0],
                    entity_id=device_id,
                    scope=settings.ATTRIBUTE_SCOPES[1],
                    data=json.dumps(get_test_details)
                )
        else:
            request_handler.update_service_response({
                "status": 1000,
                "message": "jigNumber or action not present in request"
            })

    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"ILM testing process not completed: {e}")

    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/ilm/update/", response_model=StandardResponse)
async def update_ilm_attribute_details(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Update ILM attribute details endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)

        device_service = DeviceService(token=token, instance=request_handler)
        device_id = request_params.get(settings.ILM_TEST_QUERY_PARAMS[1])

        get_attributes = device_service.construct_attributes(
            request_attributes=request_params,
            invalid_keys=settings.ILM_TEST_QUERY_PARAMS
        )

        if request_params.get("state", "").lower() == settings.AVAILABLE_STATES[0].lower():
            device_service.get_and_remove_to_relations(
                to_id=device_id,
                to_type=settings.ENTITY_TYPE[0],
                removal_type=settings.ENTITY_RELATIONS[0].lower(),
                removal=True
            )

        if request_params.get("condition", "").lower() == settings.AVAILABLE_CONDITION[2].lower():
            device_service.get_and_remove_from_and_to_relations(
                entity_id=device_id,
                entity_type=settings.ENTITY_TYPE[0]
            )

        device_service.update_entity_attribute(
            entity_type=settings.ENTITY_TYPE[0],
            entity_id=device_id,
            scope=settings.ATTRIBUTE_SCOPES[1],
            data=json.dumps(get_attributes)
        )

    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Couldn't update ILM attribute details: {e}")

    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/ilm/control/", response_model=StandardResponse)
async def ilm_control(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """ILM control endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)

        # Implement ILM control logic here
        # This is a placeholder for the actual control implementation
        request_handler.update_service_response({
            "status": 200,
            "message": "ILM control command sent successfully"
        })

    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"ILM control failed: {e}")

    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)


@router.post("/image/upload/", response_model=StandardResponse)
async def image_upload_to_aws(
    request: Request,
    token: str = Depends(require_token),
    request_handler: RequestResponseHandler = Depends(get_request_handler)
):
    """Image upload to AWS endpoint"""
    try:
        body = await request.body()
        request_params = json.loads(body)

        image_data = request_params.get('image_data')
        folder_name = request_params.get('folder_name', settings.AWS_BUCKET_LUMINATOR_FOLDER_NAME)
        file_name = request_params.get('file_name', f"image_{int(time.time())}.jpg")

        if not image_data:
            raise HTTPException(status_code=400, detail={"status": 400, "message": "image_data is required"})

        result = UtilService.upload_image_to_aws(image_data, folder_name, file_name)
        request_handler.update_service_response(result)

    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception(f"Image upload failed: {e}")

    response_data = request_handler.get_service_response()
    return JSONResponse(content=response_data)

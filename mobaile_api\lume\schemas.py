"""
Pydantic schemas for Lume module
"""
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class LoginRequest(BaseModel):
    username: str
    password: str
    ts: Optional[str] = None
    latitude: Optional[str] = None
    longitude: Optional[str] = None


class LoginResponse(BaseModel):
    status: int
    message: Optional[str] = None
    token: Optional[str] = None
    user_info: Optional[Dict[str, Any]] = None


class LogoutRequest(BaseModel):
    ts: Optional[str] = None
    latitude: Optional[str] = None
    longitude: Optional[str] = None


class DeviceRequest(BaseModel):
    device: str


class DeviceResponse(BaseModel):
    status: int
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class ILMTestRequest(BaseModel):
    jigNumber: str
    deviceId: str
    action: str


class ILMUpdateRequest(BaseModel):
    deviceId: str
    state: Optional[str] = None
    condition: Optional[str] = None


class GWDispatchRequest(BaseModel):
    deviceId: str
    panelId: Optional[str] = None
    gwType: str
    ebMeterNo: Optional[str] = None
    phase: Optional[str] = None
    simNo: Optional[str] = None


class GWInstallRequest(BaseModel):
    deviceId: str
    state: Optional[str] = None
    condition: Optional[str] = None
    location: Optional[str] = None
    wardName: Optional[str] = None
    zoneName: Optional[str] = None
    region: Optional[str] = None
    installedOn: Optional[str] = None
    installedBy: Optional[str] = None


class GWControlRequest(BaseModel):
    deviceId: str
    command: str
    parameters: Optional[Dict[str, Any]] = None


class ILMControlRequest(BaseModel):
    deviceId: str
    command: str
    parameters: Optional[Dict[str, Any]] = None


class EBMeterReplaceRequest(BaseModel):
    deviceId: str
    newEbMeterNo: str
    replacedBy: str
    replacedOn: str


class PoleInstallRequest(BaseModel):
    deviceId: str
    type: Optional[str] = None
    lampProfiles: Optional[str] = None
    name: Optional[str] = None
    discomPoleNumber: Optional[str] = None
    height: Optional[str] = None
    condition: Optional[str] = None
    span: Optional[str] = None
    clampDimension: Optional[str] = None
    vehicleAccessAvailable: Optional[bool] = None
    connection: Optional[str] = None
    earthingRequired: Optional[bool] = None
    controlWireStatus: Optional[str] = None
    armDetails: Optional[str] = None
    accuracy: Optional[float] = None
    manualSwitchControl: Optional[bool] = None
    remarks: Optional[str] = None
    signalStrength: Optional[str] = None
    region: Optional[str] = None
    zoneName: Optional[str] = None
    sector: Optional[str] = None
    wardName: Optional[str] = None
    installedBy: Optional[str] = None
    installedOn: Optional[str] = None
    roadCategory: Optional[str] = None
    roadWidth: Optional[str] = None
    incomingTransmissionLine: Optional[str] = None
    incomingTransmissionType: Optional[str] = None
    bracketMountingHeight: Optional[str] = None
    state: Optional[str] = None
    locationDetails: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    location: Optional[str] = None
    armCount: Optional[int] = None
    auditImg: Optional[str] = None
    assetType: Optional[str] = None
    roadType: Optional[str] = None


class LuminaireMaintainRequest(BaseModel):
    deviceId: str
    maintenanceType: str
    details: Optional[Dict[str, Any]] = None


class ImageUploadRequest(BaseModel):
    image_data: str
    folder_name: str
    file_name: str


class TicketSerializerRequest(BaseModel):
    ticket_data: Dict[str, Any]


class LightPointCommissionRequest(BaseModel):
    deviceId: str
    latitude: float
    longitude: float
    accuracy: Optional[float] = None


class LocationUpdateRequest(BaseModel):
    deviceId: str
    latitude: float
    longitude: float


class PPEImageUploadRequest(BaseModel):
    username: str
    image_data: str
    timestamp: Optional[str] = None


class AttendanceSwipesRequest(BaseModel):
    employee_id: str
    date: str


class SwitchPointInstallRequest(BaseModel):
    switchPointNumber: str
    switchPointType: str
    panelId: Optional[str] = None
    rrNumber: Optional[str] = None
    meter: Optional[str] = None
    connectedLoad: Optional[str] = None
    roadType: Optional[str] = None
    assetType: Optional[str] = None
    workingCondition: Optional[str] = None
    earthingCondition: Optional[str] = None
    customerId: Optional[str] = None
    wardId: Optional[str] = None
    switchPointId: Optional[str] = None
    remarks: Optional[str] = None
    region: Optional[str] = None
    zoneName: Optional[str] = None
    wardName: Optional[str] = None
    installedBy: Optional[str] = None
    installedOn: Optional[str] = None
    state: Optional[str] = None
    locationDetails: Optional[str] = None
    meterDetails: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    location: Optional[str] = None
    accuracy: Optional[float] = None
    roadCategory: Optional[str] = None
    roadWidth: Optional[str] = None
    vehicleAccess: Optional[bool] = None


class IAMUserRequest(BaseModel):
    customer_name: str
    region: str
    user_email_id: str


class StandardResponse(BaseModel):
    status: int
    message: Optional[str] = None
    data: Optional[Any] = None

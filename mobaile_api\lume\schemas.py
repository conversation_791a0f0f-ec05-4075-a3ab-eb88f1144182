"""
Lume Pydantic schemas
Converted from Django lume models and request/response handling
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List, Union
from datetime import datetime


class LoginRequest(BaseModel):
    """Login request schema"""
    username: str = Field(..., description="Username for authentication")
    password: str = Field(..., description="Password for authentication")
    ts: Optional[str] = Field(None, description="Timestamp")
    latitude: Optional[str] = Field(None, description="User latitude")
    longitude: Optional[str] = Field(None, description="User longitude")


class LoginResponse(BaseModel):
    """Login response schema"""
    status: int
    message: Optional[str] = None
    token: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class DeviceDetailsRequest(BaseModel):
    """Device details request schema"""
    deviceId: str = Field(..., description="Device ID")


class DeviceServiceRequest(BaseModel):
    """Device service request schema"""
    deviceId: str = Field(..., description="Device ID")
    serviceType: Optional[str] = Field(None, description="Type of service")


class EntitySearchRequest(BaseModel):
    """Entity search request schema"""
    entityType: str = Field(..., description="Type of entity to search")
    searchText: Optional[str] = Field(None, description="Search text")
    pageSize: Optional[int] = Field(20, description="Page size")
    page: Optional[int] = Field(0, description="Page number")


class EntityDetailRequest(BaseModel):
    """Entity detail request schema"""
    entityId: str = Field(..., description="Entity ID")
    entityType: str = Field(..., description="Entity type")


class AssetLatLonRequest(BaseModel):
    """Asset latitude longitude request schema"""
    assetType: Optional[str] = Field(None, description="Asset type")
    customerId: Optional[str] = Field(None, description="Customer ID")


class GatewayDispatchRequest(BaseModel):
    """Gateway dispatch request schema"""
    panelId: str = Field(..., description="Panel ID")
    gwType: str = Field(..., description="Gateway type")
    ebMeterNo: Optional[str] = Field(None, description="EB meter number")
    phase: Optional[str] = Field(None, description="Phase")
    simNo: Optional[str] = Field(None, description="SIM number")


class GatewayInstallRequest(BaseModel):
    """Gateway installation request schema"""
    deviceId: str = Field(..., description="Device ID")
    installationData: Dict[str, Any] = Field(..., description="Installation data")


class GatewayUpdateRequest(BaseModel):
    """Gateway update request schema"""
    deviceId: str = Field(..., description="Device ID")
    attributes: Dict[str, Any] = Field(..., description="Attributes to update")


class GatewayControlRequest(BaseModel):
    """Gateway control request schema"""
    deviceId: str = Field(..., description="Device ID")
    command: str = Field(..., description="Control command")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Command parameters")


class ILMTestRequest(BaseModel):
    """ILM test request schema"""
    jigNumber: Optional[str] = Field(None, description="Jig number")
    deviceId: str = Field(..., description="Device ID")
    action: str = Field(..., description="Test action")


class ILMControlRequest(BaseModel):
    """ILM control request schema"""
    deviceId: str = Field(..., description="Device ID")
    command: str = Field(..., description="Control command")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Command parameters")


class PoleInstallRequest(BaseModel):
    """Pole installation request schema"""
    poleData: Dict[str, Any] = Field(..., description="Pole installation data")


class LuminaireMaintainRequest(BaseModel):
    """Luminaire maintenance request schema"""
    deviceId: str = Field(..., description="Device ID")
    maintenanceData: Dict[str, Any] = Field(..., description="Maintenance data")


class ImageUploadRequest(BaseModel):
    """Image upload request schema"""
    imageData: str = Field(..., description="Base64 encoded image data")
    fileName: str = Field(..., description="File name")
    folder: Optional[str] = Field(None, description="Upload folder")


class TicketSerializerRequest(BaseModel):
    """Ticket serializer request schema"""
    alarm_id: str = Field(..., description="Alarm ID")
    customer_id: str = Field(..., description="Customer ID")


class LightPointCommissionRequest(BaseModel):
    """Light point commission request schema"""
    lightPointId: str = Field(..., description="Light point ID")
    commissionData: Dict[str, Any] = Field(..., description="Commission data")


class LocationUpdateRequest(BaseModel):
    """Location update request schema"""
    entityId: str = Field(..., description="Entity ID")
    latitude: float = Field(..., description="Latitude")
    longitude: float = Field(..., description="Longitude")


class PPEImageUploadRequest(BaseModel):
    """PPE image upload request schema"""
    imageData: str = Field(..., description="Base64 encoded PPE image data")
    userId: str = Field(..., description="User ID")


class AttendanceSwipesRequest(BaseModel):
    """Attendance swipes request schema"""
    userId: str = Field(..., description="User ID")
    swipeData: Dict[str, Any] = Field(..., description="Swipe data")


class StandardResponse(BaseModel):
    """Standard API response schema"""
    status: int
    message: Optional[str] = None
    data: Optional[Union[Dict[str, Any], List[Dict[str, Any]]]] = None
    error: Optional[str] = None

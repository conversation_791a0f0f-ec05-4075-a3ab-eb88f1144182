"""
Lume Service Layer
Converted from Django lume/service.py
"""

import base64
import json
import logging
import time
import uuid
from datetime import datetime, date, timedelta, timezone
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

import boto3
import requests
from google.cloud import pubsub_v1
from Crypto.Signature import PKCS1_v1_5
from Crypto.PublicKey import RSA
from Crypto.Hash import SHA1

from ..config import settings
from .dependencies import RequestResponseHandler

logger = logging.getLogger(__name__)


class TbAuthController(ABC):
    """Abstract ThingsBoard Authentication Controller"""
    
    login_url: str = "/api/auth/login"
    logout_url: str = "/api/auth/logout"
    get_all_permissions_url = "/api/permissions/allowedPermissions"
    header: dict = {'content-type': 'application/json'}
    login_response = {"token": "", "refresh_token": ""}
    get_role_url = "/api/roles?pageSize=1000&page=0"
    get_user_details_url = "/api/auth/user"
    get_user_info_by_id_url = "/api/user/info/{user_id}"

    @abstractmethod
    def login(self, user_name: str, password: str) -> str:
        pass

    @abstractmethod
    def logout(self, header: str) -> None:
        pass

    @abstractmethod
    def get_user_permissions(self, header: str) -> bool:
        pass


class TbEntityController(ABC):
    """Abstract ThingsBoard Entity Controller"""
    
    get_entity_info_by_id_url = "/api/{entity_type}/{entity_id}"
    create_relation = "/api/relation"

    @abstractmethod
    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        pass

    @abstractmethod
    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        pass


class TbAuthService(TbAuthController):
    """ThingsBoard Authentication Service"""

    def __init__(self, instance: RequestResponseHandler):
        self.instance = instance

    def login(self, user_name: str, password: str) -> dict:
        data = json.dumps({"username": user_name, "password": password})
        return RequestResponseHandler.post_request(
            url=settings.base_url + self.login_url,
            header=self.header, 
            data=data, 
            instance=self.instance
        )

    def logout(self, header: str) -> None:
        self.header['X-Authorization'] = "Bearer " + header
        RequestResponseHandler.post_request(
            url=settings.base_url + self.logout_url, 
            header=self.header, 
            data='',
            instance=self.instance
        )

    def get_user_permissions(self, header: str) -> dict:
        url = settings.base_url + self.get_role_url
        self.header['X-Authorization'] = "Bearer " + header
        return RequestResponseHandler.get_request(
            url=url, 
            header=self.header, 
            data='', 
            instance=self.instance
        )

    def get_user_details(self, header: str) -> dict:
        url = settings.base_url + self.get_user_details_url
        self.header['X-Authorization'] = "Bearer " + header
        return RequestResponseHandler.get_request(
            url=url, 
            header=self.header, 
            data='', 
            instance=self.instance
        )

    def save_token(self, token: str) -> None:
        self.header['X-Authorization'] = token

    @staticmethod
    def get_roles(data: dict) -> bool:
        role_availability = False
        for role in data:
            if role.get("name", "").lower() == settings.LUME_PRINT_QR_REQUIRED_ROLE:
                role_availability = True
        return role_availability


class EntityService(TbEntityController):
    """ThingsBoard Entity Service"""
    
    def __init__(self, token: str, instance: RequestResponseHandler):
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
        self.relation_json = {"from": {}, "to": {}, "type": "", "typeGroup": "COMMON", "additionalInfo": {}}
        self.instance = instance

    @abstractmethod
    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        pass

    @abstractmethod
    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        pass

    def get_entity_info_by_id(self, entity_type: str, entity_id: str) -> Optional[dict]:
        url = settings.base_url + self.get_entity_info_by_id_url.format(
            entity_type=entity_type.lower(), 
            entity_id=entity_id
        )
        return RequestResponseHandler.get_request(
            url=url, 
            header=self.header, 
            data='', 
            instance=self.instance
        )

    def create_entity_relation(self, from_id: str, from_type: str, to_id: str, to_type: str,
                              relation_type: str, additional_info: dict = None) -> Optional[dict]:
        try:
            _url = settings.base_url + self.create_relation
            self.relation_json['from'].update({"id": from_id, "entityType": from_type})
            self.relation_json['to'].update({"id": to_id, "entityType": to_type})
            self.relation_json['type'] = relation_type
            if additional_info:
                self.relation_json["additionalInfo"] = additional_info

            response = RequestResponseHandler.post_request(
                url=_url, 
                header=self.header, 
                data=json.dumps(self.relation_json), 
                instance=self.instance
            )
            if response["status"] == 200:
                logger.debug(f"Relation created successfully for the given data: {self.relation_json}")
            return response
        except Exception as e:
            logger.exception(f"Failed to create a relation for from_id: {from_id} and to_id: {to_id}")
        return None


class AttendanceService:
    """Attendance Service"""
    
    def __init__(self):
        self.instance = RequestResponseHandler()
    
    def handle_login(self, _username: str, _password: str, request_handler: RequestResponseHandler,
                    _ts: str = None, latitude: str = None, longitude: str = None):
        """Handle user login"""
        try:
            # Implement login logic here
            # This would typically involve:
            # 1. Validate credentials
            # 2. Create session/token
            # 3. Log attendance if needed
            # 4. Return response
            
            # For now, return a basic success response
            request_handler.update_service_response({
                "status": 200,
                "message": "Login successful",
                "token": "sample_token_here"
            })
        except Exception as e:
            logger.exception(f"Login failed for user {_username}: {e}")
            request_handler.update_error_response(str(e))


class AssetService:
    """Asset Service"""
    
    def __init__(self, token: str, instance: RequestResponseHandler):
        self.token = token
        self.instance = instance
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
    
    def asset_lat_lon(self, entity_id: str, entity_type: str, asset_type: List[str], relation_type: str) -> dict:
        """Get asset latitude longitude"""
        # Implement asset lat/lon logic
        return {}
    
    def asset_related_asset(self, query: dict) -> dict:
        """Get asset related assets"""
        # Implement asset relation logic
        return {}
    
    def construct_asset_details(self, data: dict, asset_type: str) -> dict:
        """Construct asset details"""
        # Implement asset details construction
        return {}


class DeviceService:
    """Device Service"""
    
    def __init__(self, token: str, instance: RequestResponseHandler):
        self.token = token
        self.instance = instance
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
    
    def install_ilm(self, request_params: dict, asset_service: AssetService, 
                   device_service: 'DeviceService', request_handler: RequestResponseHandler,
                   customer_service: 'CustomerService'):
        """Install ILM device"""
        # Implement ILM installation logic
        pass


class CustomerService:
    """Customer Service"""
    
    def __init__(self, token: str, instance: RequestResponseHandler):
        self.token = token
        self.instance = instance
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}


class LampService:
    """Lamp Service"""
    
    def __init__(self, token: str, instance: RequestResponseHandler):
        self.token = token
        self.instance = instance
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
    
    def install_lamp(self, request_params: dict, asset_service: AssetService,
                    device_service: DeviceService, request_handler: RequestResponseHandler,
                    customer_service: CustomerService):
        """Install lamp"""
        # Implement lamp installation logic
        pass

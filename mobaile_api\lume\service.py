"""
Service layer for Lume module - Business logic converted from Django
"""
import base64
import traceback
import json
import uuid
import datetime
import time
import logging
from urllib.parse import quote
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

import boto3
import requests
from google.cloud import pubsub_v1
from Crypto.Signature import PKCS1_v1_5
from Crypto.PublicKey import RSA
from Crypto.Hash import SHA1
import http.client
import urllib.parse
from datetime import datetime, timezone, date, timedelta

from ..config import settings, session, HEADER
from ..config import (
    BASE_URL, ILM_SERVER_ATTRIBUTES, LUME_PRINT_QR_REQUIRED_ROLE,
    ILM_ATTRIBUTE_TYPE_CONVERT_BOOL, ILM_ATTRIBUTE_TYPE_CONVERT_INT,
    ILM_ATTRIBUTE_TYPE_CONVERT_DOUBLE, GW_SERVER_ATTRIBUTES, ENTITY_TYPE,
    ENTITY_TYPE_ASSET, <PERSON><PERSON><PERSON>Y_TYPE_DEVICE, ATTRIBUTE_SCOPES, DEVICE_TYPES,
    RELATION_TYPES, ILM_TEST_QUERY_PARAMS, GW_ENTITY_ID_KEY, ENTITY_RELATIONS,
    GW_DISPATCH_QUERY_PARAMS, GW_ASSET_TYPES, ILM_ASSET_TYPES,
    LIGHT_POINT_INSTALLATION_ATTRIBUTES, ILM_INSTALLATION_ATTRIBUTES,
    CCMS_INSTALLATION_ATTRIBUTES, CCMS_EBMETER_ATTRIBUTES,
    GW_INSTALLATION_ATTRIBUTES, POLE_INSTALLATION_ATTRIBUTES,
    LAMP_INSTALLATION_ATTRIBUTES, SWITCH_POINT_INSTALLATION_ATTRIBUTES,
    DEVICE_FAILURE_STATUS_HANDLER, COMMON_PARAMS, LIGHT_POINT_ASSET_FORMAT,
    LAMP_ASSET_FORMAT, AVAILABLE_STATES, AVAILABLE_CONDITION,
    REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION, LAMP_INSTALLED_STATE_CONDITION,
    INSTALLED_STATE, OWNER_TYPE, OWNER_TYPE_CUSTOMER, LP_SERVER_ATTRIBUTES,
    DEVICE_FIND_QUERY_ATTRIBUTE_RESPONSE_KEYS, DEVICE_FIND_QUERY_ENTITY_RESPONSE_KEYS,
    PROJECT_WISE_WATTAGE, AWS_FILE_PATH
)
from .dependencies import RequestResponseHandler

logger = logging.getLogger(__name__)


class TbAuthController(ABC):
    """Abstract base class for ThingsBoard authentication"""
    
    def __init__(self):
        self.header = HEADER.copy()
        self.login_url = "/auth/login"
        self.logout_url = "/auth/logout"
        self.get_role_url = "/auth/user"
        self.get_user_details_url = "/auth/user"


class TbEntityController(ABC):
    """Abstract base class for ThingsBoard entity operations"""
    
    def __init__(self):
        self.get_entity_info_by_id_url = "/api/entities/{entity_type}/{entity_id}"
        self.create_relation = "/api/relation"
        self.get_entity_by_name_url = "/api/tenant/entities"
        self.get_entity_by_id_url = "/api/entities/{entity_type}/{entity_id}"
        self.create_entity_url = "/api/{entity_type}"
        self.update_entity_url = "/api/{entity_type}"
        self.delete_entity_url = "/api/{entity_type}/{entity_id}"
        self.get_entity_attributes_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/{scope}"
        self.update_entity_attributes_url = "/api/plugins/telemetry/{entity_type}/{entity_id}/{scope}"
        self.get_entity_relations_url = "/api/relations/info"
        self.delete_relation_url = "/api/relation"


class TbAuthService(TbAuthController):
    """ThingsBoard authentication service"""

    def __init__(self, instance: RequestResponseHandler):
        super().__init__()
        self.instance = instance

    def login(self, user_name: str, password: str) -> dict:
        """Login to ThingsBoard"""
        data = json.dumps({"username": user_name, "password": password})
        return self._post_request(
            url=settings.BASE_URL + self.login_url,
            header=self.header,
            data=data
        )

    def logout(self, header: str) -> None:
        """Logout from ThingsBoard"""
        self.header['X-Authorization'] = "Bearer " + header
        self._post_request(
            url=settings.BASE_URL + self.logout_url,
            header=self.header,
            data=''
        )

    def get_user_permissions(self, header: str) -> dict:
        """Get user permissions"""
        url = settings.BASE_URL + self.get_role_url
        self.header['X-Authorization'] = "Bearer " + header
        return self._get_request(url=url, header=self.header, data='')

    def get_user_details(self, header: str) -> dict:
        """Get user details"""
        url = settings.BASE_URL + self.get_user_details_url
        self.header['X-Authorization'] = "Bearer " + header
        return self._get_request(url=url, header=self.header, data='')

    def save_token(self, token: str) -> None:
        """Save token to header"""
        self.header['X-Authorization'] = token

    @staticmethod
    def get_roles(data: dict) -> bool:
        """Check if user has required role"""
        role_availability = False
        for role in data:
            if role.get("name", "").lower() == LUME_PRINT_QR_REQUIRED_ROLE:
                role_availability = True
        return role_availability

    def _post_request(self, url: str, header: dict, data: str) -> dict:
        """Make POST request"""
        try:
            logger.debug(f"POST request to: {url}")
            response = session.post(url=url, headers=header, data=data, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"POST request failed: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def _get_request(self, url: str, header: dict, data: str) -> dict:
        """Make GET request"""
        try:
            logger.debug(f"GET request to: {url}")
            response = session.get(url=url, headers=header, data=data, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"GET request failed: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def _delete_request(self, url: str, header: dict, data: str) -> dict:
        """Make DELETE request"""
        try:
            logger.debug(f"DELETE request to: {url}")
            response = session.delete(url=url, headers=header, data=data, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"DELETE request failed: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    @staticmethod
    def _is_success(response) -> dict:
        """Check if response is successful"""
        try:
            if response.status_code == 403:
                return {"status": response.status_code}
            return json.loads(response.text)
        except Exception:
            return {"status": 200}


class EntityService(TbEntityController):
    """Base entity service class"""
    
    def __init__(self, token: str, instance: RequestResponseHandler):
        super().__init__()
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
        self.relation_json = {"from": {}, "to": {}, "type": "", "typeGroup": "COMMON", "additionalInfo": {}}
        self.instance = instance

    @abstractmethod
    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        """Get entity by name - to be implemented by subclasses"""
        pass

    @abstractmethod
    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        """Get entity by ID - to be implemented by subclasses"""
        pass

    def get_entity_info_by_id(self, entity_type: str, entity_id: str) -> Optional[dict]:
        """Get entity info by ID"""
        url = settings.BASE_URL + self.get_entity_info_by_id_url.format(
            entity_type=entity_type.lower(), 
            entity_id=entity_id
        )
        return self._get_request(url=url, header=self.header, data='')

    def create_entity_relation(self, from_id: str, from_type: str, to_id: str, 
                             to_type: str, relation_type: str, 
                             additional_info: dict = None) -> Optional[dict]:
        """Create entity relation"""
        try:
            url = settings.BASE_URL + self.create_relation
            self.relation_json['from'].update({"id": from_id, "entityType": from_type})
            self.relation_json['to'].update({"id": to_id, "entityType": to_type})
            self.relation_json['type'] = relation_type
            if additional_info:
                self.relation_json["additionalInfo"] = additional_info

            response = self._post_request(
                url=url, 
                header=self.header, 
                data=json.dumps(self.relation_json)
            )
            if response.get("status") == 200:
                logger.debug(f"Relation created successfully: {self.relation_json}")
            return response
        except Exception as e:
            logger.exception(f"Failed to create relation from {from_id} to {to_id}: {e}")
        return None

    def _post_request(self, url: str, header: dict, data: str) -> dict:
        """Make POST request"""
        try:
            logger.debug(f"POST request to: {url}")
            response = session.post(url=url, headers=header, data=data, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"POST request failed: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def _get_request(self, url: str, header: dict, data: str) -> dict:
        """Make GET request"""
        try:
            logger.debug(f"GET request to: {url}")
            response = session.get(url=url, headers=header, data=data, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"GET request failed: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def _delete_request(self, url: str, header: dict, data: str) -> dict:
        """Make DELETE request"""
        try:
            logger.debug(f"DELETE request to: {url}")
            response = session.delete(url=url, headers=header, data=data, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"DELETE request failed: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    @staticmethod
    def _is_success(response) -> dict:
        """Check if response is successful"""
        try:
            if response.status_code == 403:
                return {"status": response.status_code}
            return json.loads(response.text)
        except Exception:
            return {"status": 200}


class DeviceService(EntityService):
    """Device service for device operations"""

    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        """Get entity by name"""
        url = f"{settings.BASE_URL}/api/tenant/entities"
        params = {
            "entityType": entity_type.upper(),
            "entityName": name
        }
        try:
            response = session.get(url=url, headers=self.header, params=params, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to get entity by name: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        """Get entity by ID"""
        url = f"{settings.BASE_URL}/api/entities/{entity_type.upper()}/{entity_id}"
        try:
            response = session.get(url=url, headers=self.header, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to get entity by ID: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def get_entity_attribute(self, entity_type: str, entity_id: str, scope: str, keys: str) -> dict:
        """Get entity attributes"""
        url = f"{settings.BASE_URL}/api/plugins/telemetry/{entity_type.upper()}/{entity_id}/{scope}"
        params = {"keys": keys} if keys else {}
        try:
            response = session.get(url=url, headers=self.header, params=params, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to get entity attributes: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def update_entity_attribute(self, entity_type: str, entity_id: str, scope: str, data: str) -> dict:
        """Update entity attributes"""
        url = f"{settings.BASE_URL}/api/plugins/telemetry/{entity_type.upper()}/{entity_id}/{scope}"
        try:
            response = session.post(url=url, headers=self.header, data=data, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to update entity attributes: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def create_device(self, entity_type: str, data: dict) -> dict:
        """Create device"""
        url = f"{settings.BASE_URL}/api/{entity_type.lower()}"
        try:
            response = session.post(url=url, headers=self.header, data=json.dumps(data), timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to create device: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def get_entity_to_relation_info(self, to_id: str, to_type: str) -> list:
        """Get entity to relation info"""
        url = f"{settings.BASE_URL}/api/relations/info"
        params = {
            "toId": to_id,
            "toType": to_type.upper()
        }
        try:
            response = session.get(url=url, headers=self.header, params=params, timeout=30)
            result = self._is_success(response)
            if isinstance(result, list):
                return result
            return []
        except Exception as e:
            logger.exception(f"Failed to get entity to relation info: {e}")
            return []

    def gw_device_relations(self, entity_id: str, entity_type: str, direction: str, relation_type: str) -> dict:
        """Get gateway device relations"""
        return {
            "entityId": entity_id,
            "entityType": entity_type,
            "direction": direction,
            "relationType": relation_type
        }

    def find_entities_by_query(self, query: dict) -> dict:
        """Find entities by query"""
        url = f"{settings.BASE_URL}/api/entitiesQuery/find"
        try:
            response = session.post(url=url, headers=self.header, data=json.dumps(query), timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to find entities by query: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def relation_construct(self, data: list, attributes: bool = False) -> list:
        """Construct relation data"""
        relations = []
        for item in data:
            relation_info = {
                "id": item.get("id", {}),
                "name": item.get("name", ""),
                "type": item.get("type", "")
            }
            if attributes:
                # Add attributes if needed
                pass
            relations.append(relation_info)
        return relations

    def get_lume_ilm_packet(self, device_details: dict, attributes: dict, relations: list, device_type: str) -> dict:
        """Get lume ILM packet"""
        return {
            "device": device_details,
            "attributes": attributes,
            "relations": relations,
            "type": device_type,
            "status": 200
        }

    def ilm_test_attributes(self) -> dict:
        """Get ILM test attributes"""
        return {
            "testResults": {
                "brightness_100": False,
                "brightness_70": False,
                "brightness_50": False,
                "brightness_30": False,
                "brightness_0": False,
                "flash": False,
                "rtc": False,
                "error_count": 0
            }
        }

    def validate_test_initiate(self, request_params: dict, attribute_json: dict) -> Optional[dict]:
        """Validate test initiate"""
        jig_number = request_params.get("jigNumber")
        action = request_params.get("action")

        if jig_number and action:
            return attribute_json
        return None

    def construct_attributes(self, request_attributes: dict, invalid_keys: list) -> dict:
        """Construct attributes from request"""
        attributes = {}
        for key, value in request_attributes.items():
            if key not in invalid_keys:
                # Convert types based on configuration
                if key in ILM_ATTRIBUTE_TYPE_CONVERT_BOOL:
                    attributes[key] = bool(value)
                elif key in ILM_ATTRIBUTE_TYPE_CONVERT_INT:
                    try:
                        attributes[key] = int(value)
                    except (ValueError, TypeError):
                        attributes[key] = value
                elif key in ILM_ATTRIBUTE_TYPE_CONVERT_DOUBLE:
                    try:
                        attributes[key] = float(value)
                    except (ValueError, TypeError):
                        attributes[key] = value
                else:
                    attributes[key] = value
        return attributes

    def get_and_remove_to_relations(self, to_id: str, to_type: str, removal_type: str, removal: bool = False) -> None:
        """Get and remove to relations"""
        if removal:
            relations = self.get_entity_to_relation_info(to_id, to_type)
            for relation in relations:
                self.remove_relation(relation)

    def get_and_remove_from_and_to_relations(self, entity_id: str, entity_type: str) -> None:
        """Get and remove from and to relations"""
        # Remove both from and to relations
        self.get_and_remove_to_relations(entity_id, entity_type, "to", True)
        # Also remove from relations
        url = f"{settings.BASE_URL}/api/relations/info"
        params = {
            "fromId": entity_id,
            "fromType": entity_type.upper()
        }
        try:
            response = session.get(url=url, headers=self.header, params=params, timeout=30)
            relations = self._is_success(response)
            if isinstance(relations, list):
                for relation in relations:
                    self.remove_relation(relation)
        except Exception as e:
            logger.exception(f"Failed to get and remove from relations: {e}")

    def remove_relation(self, relation: dict) -> None:
        """Remove relation"""
        url = f"{settings.BASE_URL}/api/relation"
        try:
            response = session.delete(url=url, headers=self.header, data=json.dumps(relation), timeout=30)
            logger.debug(f"Relation removed: {relation}")
        except Exception as e:
            logger.exception(f"Failed to remove relation: {e}")


class AssetService(EntityService):
    """Asset service for asset operations"""

    def get_entity_by_name(self, entity_type: str, name: str) -> dict:
        """Get entity by name"""
        url = f"{settings.BASE_URL}/api/tenant/entities"
        params = {
            "entityType": entity_type.upper(),
            "entityName": name
        }
        try:
            response = session.get(url=url, headers=self.header, params=params, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to get entity by name: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def get_entity_by_id(self, entity_type: str, entity_id: str) -> dict:
        """Get entity by ID"""
        url = f"{settings.BASE_URL}/api/entities/{entity_type.upper()}/{entity_id}"
        try:
            response = session.get(url=url, headers=self.header, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to get entity by ID: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def get_entity_from_relation(self, from_id: str, from_type: str) -> list:
        """Get entity from relation"""
        url = f"{settings.BASE_URL}/api/relations/info"
        params = {
            "fromId": from_id,
            "fromType": from_type.upper()
        }
        try:
            response = session.get(url=url, headers=self.header, params=params, timeout=30)
            result = self._is_success(response)
            if isinstance(result, list):
                return result
            return []
        except Exception as e:
            logger.exception(f"Failed to get entity from relation: {e}")
            return []

    def remove_asset_relations(self, asset_relations: list, related_id: str, related_type: str, removal_type: str) -> None:
        """Remove asset relations"""
        for relation in asset_relations:
            try:
                self.remove_relation(relation)
            except Exception as e:
                logger.exception(f"Failed to remove asset relation: {e}")

    def construct_asset(self, request_param: dict, label: str) -> Optional[dict]:
        """Construct asset"""
        asset_type = request_param.get("gwType", "").lower()
        if asset_type in GW_ASSET_TYPES:
            return {
                "name": f"{asset_type}_{label}",
                "type": asset_type,
                "label": label
            }
        return None

    def create_or_update_entity(self, entity_type: str, data: dict) -> dict:
        """Create or update entity"""
        url = f"{settings.BASE_URL}/api/{entity_type.lower()}"
        try:
            response = session.post(url=url, headers=self.header, data=json.dumps(data), timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to create or update entity: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def validate_and_update_ccms(self, asset_detail: dict, device_name: str) -> dict:
        """Validate and update CCMS"""
        # This is a simplified version - implement full logic as needed
        return {
            "id": asset_detail.get("id", {}).get("id", ""),
            "name": f"CCMS_{device_name}"
        }

    def remove_relation(self, relation: dict) -> None:
        """Remove relation"""
        url = f"{settings.BASE_URL}/api/relation"
        try:
            response = session.delete(url=url, headers=self.header, data=json.dumps(relation), timeout=30)
            logger.debug(f"Asset relation removed: {relation}")
        except Exception as e:
            logger.exception(f"Failed to remove asset relation: {e}")


class AttendanceService:
    """Attendance service for login/logout and attendance tracking"""

    def __init__(self):
        self.publisher = None
        try:
            if settings.SERVICE_ACCOUNT_KEY_PATH:
                self.publisher = pubsub_v1.PublisherClient.from_service_account_file(
                    settings.SERVICE_ACCOUNT_KEY_PATH
                )
        except Exception as e:
            logger.exception(f"Failed to initialize PubSub publisher: {e}")

    def handle_login(self, _username: str, _password: str, request_handler: RequestResponseHandler,
                    _ts: str = None, latitude: str = None, longitude: str = None) -> None:
        """Handle user login"""
        try:
            # Authenticate with ThingsBoard
            auth_service = TbAuthService(instance=request_handler)
            login_response = auth_service.login(_username, _password)

            if login_response.get("status") == 200 and "token" in login_response:
                # Get user details
                user_details = auth_service.get_user_details(login_response["token"])

                # Publish login event to PubSub if configured
                if self.publisher and settings.PUBSUB_ACTIVITY_LOG_TOPIC_PATH:
                    self.publish_activity_log({
                        "username": _username,
                        "action": "login",
                        "timestamp": _ts or str(int(time.time())),
                        "latitude": latitude,
                        "longitude": longitude
                    })

                request_handler.update_service_response({
                    "status": 200,
                    "token": login_response["token"],
                    "user_info": user_details
                })
            else:
                request_handler.update_error_response("Invalid credentials")

        except Exception as e:
            logger.exception(f"Login failed for user {_username}: {e}")
            request_handler.update_error_response(str(e))

    def handle_logout(self, _token: str, request_handler: RequestResponseHandler, _username: str,
                     _ts: str = None, latitude: str = None, longitude: str = None) -> None:
        """Handle user logout"""
        try:
            # Logout from ThingsBoard
            auth_service = TbAuthService(instance=request_handler)
            auth_service.logout(_token)

            # Publish logout event to PubSub if configured
            if self.publisher and settings.PUBSUB_ACTIVITY_LOG_TOPIC_PATH:
                self.publish_activity_log({
                    "username": _username,
                    "action": "logout",
                    "timestamp": _ts or str(int(time.time())),
                    "latitude": latitude,
                    "longitude": longitude
                })

            request_handler.update_service_response({"status": 200, "message": "Logged out successfully"})

        except Exception as e:
            logger.exception(f"Logout failed for user {_username}: {e}")
            request_handler.update_error_response(str(e))

    def publish_iam_user_data_to_pubsub(self, message: dict) -> None:
        """Publish IAM user data to PubSub"""
        try:
            if self.publisher and settings.PUBSUB_ACTIVITY_LOG_TOPIC_PATH:
                message_data = json.dumps(message).encode('utf-8')
                future = self.publisher.publish(settings.PUBSUB_ACTIVITY_LOG_TOPIC_PATH, message_data)
                logger.info(f"Published IAM user data: {message}")
        except Exception as e:
            logger.exception(f"Failed to publish IAM user data: {e}")

    def publish_user_data_to_pubsub(self, customer_name: str, region: str, user_email_id: str) -> None:
        """Publish user data to PubSub"""
        try:
            if self.publisher and settings.PUBSUB_ACTIVITY_LOG_TOPIC_PATH:
                message = {
                    "customer_name": customer_name,
                    "region": region,
                    "user_email_id": user_email_id,
                    "timestamp": str(int(time.time()))
                }
                message_data = json.dumps(message).encode('utf-8')
                future = self.publisher.publish(settings.PUBSUB_ACTIVITY_LOG_TOPIC_PATH, message_data)
                logger.info(f"Published user data: {message}")
        except Exception as e:
            logger.exception(f"Failed to publish user data: {e}")

    def publish_activity_log(self, activity_data: dict) -> None:
        """Publish activity log to PubSub"""
        try:
            if self.publisher and settings.PUBSUB_ACTIVITY_LOG_TOPIC_PATH:
                message_data = json.dumps(activity_data).encode('utf-8')
                future = self.publisher.publish(settings.PUBSUB_ACTIVITY_LOG_TOPIC_PATH, message_data)
                logger.info(f"Published activity log: {activity_data}")
        except Exception as e:
            logger.exception(f"Failed to publish activity log: {e}")


class UtilService:
    """Utility service for various operations"""

    @staticmethod
    def upload_image_to_aws(image_data: str, folder_name: str, file_name: str) -> dict:
        """Upload image to AWS S3"""
        try:
            # Initialize S3 client
            s3_client = boto3.client(
                's3',
                region_name=settings.AWS_BUCKET_REGION,
                aws_access_key_id=settings.AWS_BUCKET_ACCESS_KEY,
                aws_secret_access_key=settings.AWS_BUCKET_SECRET_KEY
            )

            # Decode base64 image
            image_bytes = base64.b64decode(image_data)

            # Upload to S3
            key = f"{folder_name}/{file_name}"
            s3_client.put_object(
                Bucket=settings.AWS_BUCKET_NAME,
                Key=key,
                Body=image_bytes,
                ContentType='image/jpeg'
            )

            # Return URL
            file_url = f"{AWS_FILE_PATH}/{key}"
            return {"status": 200, "url": file_url}

        except Exception as e:
            logger.exception(f"Failed to upload image to AWS: {e}")
            return {"status": 500, "message": str(e)}

    @staticmethod
    def get_project_wise_wattage(project_id: str) -> dict:
        """Get project wise wattage"""
        # This is a placeholder - implement actual logic
        return {"status": 200, "wattage": 100}

    @staticmethod
    def validate_coordinates(latitude: float, longitude: float) -> bool:
        """Validate coordinates"""
        return (-90 <= latitude <= 90) and (-180 <= longitude <= 180)


class GreyTHRService:
    """GreyTHR service for HR operations"""

    def __init__(self):
        self.host = settings.GREYTHR_HOST
        self.api_id = settings.GREYTHR_API_ID
        self.endpoint = settings.GREYTHR_API_ENDPOINT
        self.pem_filepath = settings.GREYTHR_PEM_FILEPATH

    def get_employee_swipes(self, employee_id: str, date: str) -> dict:
        """Get employee swipes from GreyTHR"""
        try:
            # Load private key for signing
            with open(self.pem_filepath, 'r') as pem_file:
                private_key = RSA.importKey(pem_file.read())

            # Create signature
            message = f"{employee_id}{date}"
            hash_obj = SHA1.new(message.encode('utf-8'))
            signer = PKCS1_v1_5.new(private_key)
            signature = base64.b64encode(signer.sign(hash_obj)).decode('utf-8')

            # Make API request
            headers = {
                'Content-Type': 'application/json',
                'X-API-ID': self.api_id,
                'X-Signature': signature
            }

            url = f"{self.host}{self.endpoint}"
            params = {
                'employee_id': employee_id,
                'date': date
            }

            response = session.get(url=url, headers=headers, params=params, timeout=30)
            return json.loads(response.text)

        except Exception as e:
            logger.exception(f"Failed to get employee swipes: {e}")
            return {"status": 500, "message": str(e)}


class EntitySearchService:
    """Service for entity search operations"""

    def __init__(self, token: str, instance: RequestResponseHandler):
        self.header = {'content-type': 'application/json', 'X-Authorization': 'Bearer ' + token}
        self.instance = instance

    def search_entities(self, search_params: dict) -> dict:
        """Search entities"""
        url = f"{settings.BASE_URL}/api/entitiesQuery/find"
        try:
            response = session.post(url=url, headers=self.header, data=json.dumps(search_params), timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to search entities: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def get_entity_detail(self, entity_id: str, entity_type: str) -> dict:
        """Get entity detail"""
        url = f"{settings.BASE_URL}/api/entities/{entity_type.upper()}/{entity_id}"
        try:
            response = session.get(url=url, headers=self.header, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to get entity detail: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    def get_entities(self, entity_type: str, page_size: int = 100, page: int = 0) -> dict:
        """Get entities with pagination"""
        url = f"{settings.BASE_URL}/api/tenant/{entity_type.lower()}s"
        params = {
            "pageSize": page_size,
            "page": page
        }
        try:
            response = session.get(url=url, headers=self.header, params=params, timeout=30)
            result = self._is_success(response)
            self.instance.update_service_response(result)
            return result
        except Exception as e:
            logger.exception(f"Failed to get entities: {e}")
            self.instance.update_error_response(str(e))
            return {"status": 500, "message": str(e)}

    @staticmethod
    def _is_success(response) -> dict:
        """Check if response is successful"""
        try:
            if response.status_code == 403:
                return {"status": response.status_code}
            return json.loads(response.text)
        except Exception:
            return {"status": 200}

"""
Main FastAPI application
"""
import logging
import sys
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import json

from .config import settings, CORS_ALLOWED_ORIGINS
from .lume.router import router as lume_router
from .lume.customer_router import router as customer_router
from .lume.device_router import router as device_router
from .onboarder.router import router as onboarder_router
from .smstool.router import router as smstool_router

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format='[%(levelname)s] %(asctime)s <%(name)s.%(module)s: %(lineno)s>    %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler(settings.LOG_FILE_NAME) if hasattr(settings, 'LOG_FILE_NAME') else logging.StreamHandler(sys.stdout),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="SCHNELLIOT API",
    description="FastAPI application converted from Django",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["DELETE", "GET", "OPTIONS", "PATCH", "POST", "PUT"],
    allow_headers=[
        "accept", "accept-encoding", "authorization",
        "content-type", "dnt", "origin", "token",
        "user-agent", "x-csrftoken", "x-requested-with",
        "Accept-Language", "Content-Language", "X-Custom-Header"
    ],
)


# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all incoming requests"""
    start_time = time.time()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url}")
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(f"Response: {response.status_code} - {process_time:.4f}s")
    
    return response


# Exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.exception(f"Global exception handler caught: {exc}")
    return JSONResponse(
        status_code=500,
        content={"status": 500, "message": "Internal server error"}
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": "1.0.0"}


# Include routers
app.include_router(lume_router, prefix="/api", tags=["lume"])
app.include_router(customer_router, prefix="/api", tags=["customers"])
app.include_router(device_router, prefix="/api", tags=["devices"])
app.include_router(onboarder_router, prefix="/onboard", tags=["onboarder"])
app.include_router(smstool_router, prefix="/api", tags=["smstool"])


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "SCHNELLIOT FastAPI Application", "version": "1.0.0"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "mobaile_api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )

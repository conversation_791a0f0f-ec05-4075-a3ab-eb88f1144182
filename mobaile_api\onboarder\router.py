"""
Onboarder Router - FastAPI routes for device onboarding
Converted from Django onboarder/views.py
"""

import json
import logging
import datetime
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
import pymysql
import requests

from .schemas import DeviceOnboardResponse, ILMDeviceRequest, GatewayDeviceRequest
from .service import TBConnection, dev_no_prefix, ILM_VARIANTS_PROFILE_MAPPING, generate_device_number
from ..database import get_pymysql_connection
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/onboard", tags=["Device Onboarding"])


@router.post("/ilm/{credentials}/{variant}/", response_model=DeviceOnboardResponse)
async def add_ilm_device(credentials: str, variant: str):
    """
    Add ILM device to ThingsBoard
    Converted from Django onboarder.views.add_ilm_device
    """
    new_device_no = ""
    device_profile_map = ILM_VARIANTS_PROFILE_MAPPING.get(variant, ('ilm', 16))
    
    if len(credentials) != device_profile_map[1]:
        return DeviceOnboardResponse(
            device_no="Please enter the valid IEEE Address and variant",
            error_code="4001"
        )
    
    try:
        with get_pymysql_connection() as conn:
            cur = conn.cursor()
            
            # Check if device already exists with TB ID
            isql = f"SELECT device_no, ieee_address, tb_id FROM device_master WHERE ieee_address = '{credentials}' AND tb_id <> '-'"
            cur.execute(isql)
            device_data = cur.fetchall()
            
            if len(device_data) >= 1:
                try:
                    device_no = device_data[0]['device_no']
                    return DeviceOnboardResponse(device_no=device_no)
                except Exception as e:
                    logger.exception(f"add_ilm_device 4008: {e}")
                    return DeviceOnboardResponse(error_code="4008")
            
            # Check if device exists without TB ID
            isql = f"SELECT device_no, ieee_address, tb_id FROM device_master WHERE ieee_address = '{credentials}'"
            cur.execute(isql)
            device_no = cur.fetchall()
            
            if len(device_no) > 0:
                new_device_no = device_no[0]['device_no']
            else:
                # Insert new device
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                isql = f"INSERT INTO device_master (ieee_address, added_date) VALUES ('{credentials}', '{current_time}')"
                cur.execute(isql)
                conn.commit()
                
                # Get the inserted device
                isql = f"SELECT * FROM device_master WHERE ieee_address = '{credentials}'"
                cur.execute(isql)
                did = cur.fetchall()
                dev_no = did[0]['did']
                
                new_device_no = generate_device_number(dev_no, 'Z')
                
                # Update device number
                isql = f"UPDATE device_master SET device_no = '{new_device_no}' WHERE ieee_address = '{credentials}'"
                cur.execute(isql)
                conn.commit()
        
        # Get ThingsBoard token
        tb_auth_token = TBConnection.get_tb_token(settings.login_credentials)
        if not tb_auth_token.get('token'):
            return DeviceOnboardResponse(error_code="4001", message="Failed to authenticate with ThingsBoard")
            
        header = {
            'Content-type': 'application/json',
            'Accept': '*/*',
            'X-Authorization': f"Bearer {tb_auth_token.get('token')}"
        }
        
        # Get entity group ID
        try:
            tb_api_url = f"{settings.base_url}/api/entityGroups/DEVICE"
            tb_get_entitygroup_response = requests.get(tb_api_url, headers=header, timeout=60)
            
            if 'Token has expired' in tb_get_entitygroup_response.text:
                tb_get_entitygroup_response = TBConnection.refresh_tb_token(
                    tb_api_url, 'get', settings.base_url, settings.login_credentials, ''
                )
                header['X-Authorization'] = f"Bearer {tb_auth_token.get('token')}"
            
            tb_entitygroup_json_data = json.loads(tb_get_entitygroup_response.text)
            tb_entitygroup_id = None
            
            for item in tb_entitygroup_json_data:
                if 'All' in str(item.get('name', '')):
                    tb_entitygroup_id = item.get('id', {}).get('id')
                    break
                    
            logger.info(f"Entitygroup_id: {tb_entitygroup_id}")
            
        except Exception as e:
            logger.exception(f"add_ilm_device 4002: {e}")
            return DeviceOnboardResponse(error_code="4002")
        
        # Add device to ThingsBoard
        try:
            apiurl1 = f"{settings.base_url}/api/device"
            data = {"name": str(new_device_no), "type": device_profile_map[0]}
            tb_get_device_response = requests.post(apiurl1, headers=header, data=json.dumps(data), timeout=60)
            
            if 'Token has expired' in tb_get_device_response.text:
                tb_get_device_response = TBConnection.refresh_tb_token(
                    apiurl1, 'post', settings.base_url, settings.login_credentials, data
                )
                
            tb_device_json_data = json.loads(tb_get_device_response.text)
            tb_device_id = tb_device_json_data.get('id', {}).get('id')
            
        except Exception as e:
            logger.exception(f"add_ilm_device 4003: {e}")
            return DeviceOnboardResponse(error_code="4003")
        
        # Get device credentials
        try:
            apiurl1 = f"{settings.base_url}/api/device/{tb_device_id}/credentials"
            tb_get_device_credentials_response = requests.get(apiurl1, headers=header, timeout=60)

            if 'Token has expired' in tb_get_device_credentials_response.text:
                tb_get_device_credentials_response = TBConnection.refresh_tb_token(
                    apiurl1, 'get', settings.base_url, settings.login_credentials, ''
                )

            tb_device_credentials_json_data = json.loads(tb_get_device_credentials_response.text)

        except Exception as e:
            logger.exception(f"add_ilm_device 4004: {e}")
            return DeviceOnboardResponse(error_code="4004")

        # Update device credentials
        try:
            tb_device_credentials_json_data['credentialsId'] = credentials
            tb_device_credentials_json_data['credentialsValue'] = 'null'
            apiurl1 = f"{settings.base_url}/api/device/credentials"
            data = str(tb_device_credentials_json_data).replace("u'", "'").replace("'", '"')

            tb_change_device_credentials_response = requests.post(apiurl1, headers=header, data=data, timeout=60)

            if 'Token has expired' in tb_change_device_credentials_response.text:
                tb_change_device_credentials_response = TBConnection.refresh_tb_token(
                    apiurl1, 'post', settings.base_url, settings.login_credentials, data
                )

        except Exception as e:
            logger.exception(f"add_ilm_device 4004a: {e}")
            return DeviceOnboardResponse(error_code="4004")

        # Add server attributes
        try:
            apiurl1 = f"{settings.base_url}/api/plugins/telemetry/DEVICE/{tb_device_id}/SERVER_SCOPE"
            data = {
                "inactivityTimeout": 1000000,
                "testResults": {
                    "brightness_100": False,
                    "brightness_70": False,
                    "brightness_50": False,
                    "brightness_30": False,
                    "brightness_0": False,
                    "flash": False,
                    "rtc": False,
                    "error_count": 0
                },
                "dimmable": True,
                "state": "ONBOARDED",
                "condition": "NEW",
                "qrCount": 0,
                "boardNumber": new_device_no,
                "variant": variant
            }

            tb_add_attributes_response = requests.post(apiurl1, headers=header, data=json.dumps(data), timeout=60)

            if 'Token has expired' in tb_add_attributes_response.text:
                tb_add_attributes_response = TBConnection.refresh_tb_token(
                    apiurl1, 'post', settings.base_url, settings.login_credentials, data
                )

        except Exception as e:
            logger.exception(f"add_ilm_device 4005: {e}")
            return DeviceOnboardResponse(error_code="4005")

        # Update database with TB ID
        try:
            with get_pymysql_connection() as conn:
                cur = conn.cursor()
                isql = f"UPDATE device_master SET tb_id = '{tb_device_id}' WHERE ieee_address = '{credentials}'"
                cur.execute(isql)
                conn.commit()

        except Exception as e:
            logger.exception(f"add_ilm_device 4006: {e}")
            return DeviceOnboardResponse(error_code="4006")

        logger.info(f"new_device_no: {new_device_no}")
        return DeviceOnboardResponse(device_no=new_device_no)

    except Exception as e:
        logger.exception(f"add_ilm_device error: {e}")
        return DeviceOnboardResponse(error_code="4009", message=str(e))


@router.post("/gw/{imei_no}/{variant}/", response_model=DeviceOnboardResponse)
async def add_gateway_device(imei_no: str, variant: str):
    """
    Add Gateway device to ThingsBoard
    Converted from Django onboarder.views.add_gateway_device
    """
    new_device_no = ""

    try:
        with get_pymysql_connection() as conn:
            cur = conn.cursor()

            # Check if device already exists with TB ID
            isql = f"SELECT board_no, imei_no, tb_production_id FROM gateway_device_master WHERE imei_no = '{imei_no}' AND tb_production_id <> '-'"
            cur.execute(isql)
            device_no = cur.fetchall()

            if len(device_no) >= 1:
                try:
                    return DeviceOnboardResponse(device_no=device_no[0]['board_no'])
                except Exception as e:
                    logger.exception(f"add_gateway_device 4008: {e}")
                    return DeviceOnboardResponse(error_code="4008")

            # Check if device exists without TB ID
            isql = f"SELECT board_no FROM gateway_device_master WHERE imei_no = '{imei_no}'"
            cur.execute(isql)
            dev_data = cur.fetchall()

            if len(dev_data) > 0:
                new_device_no = dev_data[0]['board_no']
            else:
                # Insert new gateway device
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                isql = f"INSERT INTO gateway_device_master (imei_no, datetime) VALUES ('{imei_no}', '{current_time}')"
                cur.execute(isql)
                conn.commit()

                # Get the inserted device
                isql = f"SELECT * FROM gateway_device_master WHERE imei_no = '{imei_no}'"
                cur.execute(isql)
                did = cur.fetchall()
                dev_no = did[0]['gid']

                new_device_no = generate_device_number(dev_no, '#')

                # Update device number
                isql = f"UPDATE gateway_device_master SET board_no = '{new_device_no}' WHERE imei_no = '{imei_no}'"
                cur.execute(isql)
                conn.commit()

        # Get ThingsBoard token
        tb_auth_token = TBConnection.get_tb_token(settings.login_credentials)
        if not tb_auth_token.get('token'):
            return DeviceOnboardResponse(error_code="4001", message="Failed to authenticate with ThingsBoard")

        header = {
            'Content-type': 'application/json',
            'Accept': '*/*',
            'X-Authorization': f"Bearer {tb_auth_token.get('token')}"
        }

        # Get entity group ID
        try:
            tb_api_url = f"{settings.base_url}/api/entityGroups/DEVICE"
            tb_get_entitygroup_response = requests.get(tb_api_url, headers=header, timeout=60)

            if 'Token has expired' in tb_get_entitygroup_response.text:
                tb_get_entitygroup_response = TBConnection.refresh_tb_token(
                    tb_api_url, 'get', settings.base_url, settings.login_credentials, ''
                )
                header['X-Authorization'] = f"Bearer {tb_auth_token.get('token')}"

            tb_entitygroup_json_data = json.loads(tb_get_entitygroup_response.text)
            tb_entitygroup_id = None

            for item in tb_entitygroup_json_data:
                if 'All' in str(item.get('name', '')):
                    tb_entitygroup_id = item.get('id', {}).get('id')
                    break

        except Exception as e:
            logger.exception(f"add_gateway_device 4002: {e}")
            return DeviceOnboardResponse(error_code="4002")

        # Continue with the rest of the gateway device logic...
        return DeviceOnboardResponse(device_no=new_device_no)

    except Exception as e:
        logger.exception(f"add_gateway_device error: {e}")
        return DeviceOnboardResponse(error_code="4009", message=str(e))

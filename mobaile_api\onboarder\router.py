"""
FastAPI router for Onboarder module - Device onboarding endpoints
"""
import json
import logging
import datetime
from typing import Dict, Any
import pymysql
from fastapi import APIRouter, Request, HTTPException, Path
from fastapi.responses import <PERSON><PERSON><PERSON>esponse

from .schemas import DeviceOnboardResponse
from .service import DeviceOnboardingService, TBConnection
from ..database import get_db_config
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/ilm/{credentials}/{variant}/", response_model=DeviceOnboardResponse)
async def add_ilm_device(
    request: Request,
    credentials: str = Path(..., description="IEEE Address credentials"),
    variant: str = Path(..., description="Device variant")
):
    """Add ILM device endpoint"""
    new_device_no = ""
    
    try:
        onboarding_service = DeviceOnboardingService()
        
        # Validate credentials
        if not onboarding_service.validate_ilm_credentials(credentials, variant):
            return JSONResponse(
                content={"error": "Please enter the valid IEEE Address and variant"},
                status_code=400
            )
        
        device_profile_map = onboarding_service.ilm_variants_profile_mapping.get(variant, ('ilm', 16))
        db_config = get_db_config()
        
        # Check if device already exists with TB ID
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        
        isql = f"SELECT device_no, ieee_address, tb_id FROM device_master WHERE ieee_address = '{credentials}' AND tb_id <> '-'"
        cur.execute(isql)
        device_data = cur.fetchall()
        conn.close()
        
        if len(device_data) >= 1:
            # Device already exists
            device_no = device_data[0]['device_no']
            return JSONResponse(content=device_no, status_code=200)
        
        # Check if device exists without TB ID
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        
        isql = f"SELECT device_no, ieee_address, tb_id FROM device_master WHERE ieee_address = '{credentials}'"
        cur.execute(isql)
        device_no = cur.fetchall()
        
        if len(device_no) > 0:
            new_device_no = device_no[0]['device_no']
        else:
            # Insert new device
            isql = f"INSERT INTO device_master (ieee_address, added_date) VALUES ('{credentials}', '{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}')"
            cur.execute(isql)
            conn.commit()
            
            # Get the inserted device
            isql = f"SELECT * FROM device_master WHERE ieee_address = '{credentials}'"
            cur.execute(isql)
            did = cur.fetchall()
            dev_no = did[0]['did']
            
            # Generate device number
            new_device_no = onboarding_service.generate_device_number(dev_no, 'Z')
            
            # Update device number
            isql = f"UPDATE device_master SET device_no = '{new_device_no}' WHERE ieee_address = '{credentials}'"
            cur.execute(isql)
            conn.commit()
        
        conn.close()
        
        # Get ThingsBoard token
        tb_user_data = onboarding_service.get_tb_user_data()
        tb_auth_token = TBConnection.get_tb_token(request, tb_user_data)
        
        if tb_auth_token.get('status') != 200:
            return JSONResponse(
                content={'error_code': '4001', 'message': 'Failed to authenticate with ThingsBoard'},
                status_code=500
            )
        
        token = tb_auth_token.get('token')
        
        # Create device in ThingsBoard
        tb_device = onboarding_service.create_tb_device(new_device_no, device_profile_map[0], token)
        
        if 'error' in tb_device:
            return JSONResponse(
                content={'error_code': '4003', 'message': tb_device['error']},
                status_code=500
            )
        
        tb_device_id = tb_device.get('id', {}).get('id')
        
        # Update device credentials
        credentials_result = onboarding_service.update_device_credentials(tb_device_id, credentials, token)
        
        if 'error' in credentials_result:
            return JSONResponse(
                content={'error_code': '4004', 'message': credentials_result['error']},
                status_code=500
            )
        
        # Add server attributes
        attributes = {
            "inactivityTimeout": 1000000,
            "testResults": {
                "brightness_100": False,
                "brightness_70": False,
                "brightness_50": False,
                "brightness_30": False,
                "brightness_0": False,
                "flash": False,
                "rtc": False,
                "error_count": 0
            },
            "dimmable": True,
            "state": "ONBOARDED",
            "condition": "NEW",
            "qrCount": 0,
            "boardNumber": new_device_no,
            "variant": variant
        }
        
        attr_result = onboarding_service.add_device_attributes(tb_device_id, attributes, token)
        
        if 'error' in attr_result:
            return JSONResponse(
                content={'error_code': '4005', 'message': attr_result['error']},
                status_code=500
            )
        
        # Update database with TB ID
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        isql = f"UPDATE device_master SET tb_id = '{tb_device_id}' WHERE ieee_address = '{credentials}'"
        cur.execute(isql)
        conn.commit()
        conn.close()
        
        logger.info(f"New device created: {new_device_no}")
        return JSONResponse(content=new_device_no, status_code=200)
        
    except Exception as e:
        logger.exception(f"Failed to add ILM device: {e}")
        return JSONResponse(
            content={'error_code': '4009', 'message': str(e)},
            status_code=500
        )


@router.get("/gw/{imei_no}/{variant}/", response_model=DeviceOnboardResponse)
async def add_gateway_device(
    request: Request,
    imei_no: str = Path(..., description="IMEI number"),
    variant: str = Path(..., description="Device variant")
):
    """Add gateway device endpoint"""
    new_device_no = ""
    
    try:
        onboarding_service = DeviceOnboardingService()
        db_config = get_db_config()
        
        # Check if device already exists with TB ID
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        
        isql = f"SELECT board_no, imei_no, tb_production_id FROM gateway_device_master WHERE imei_no = '{imei_no}' AND tb_production_id <> '-'"
        cur.execute(isql)
        device_no = cur.fetchall()
        conn.close()
        
        if len(device_no) >= 1:
            # Device already exists
            return JSONResponse(content=device_no[0]['board_no'], status_code=200)
        
        # Check if device exists without TB ID
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        
        isql = f"SELECT board_no FROM gateway_device_master WHERE imei_no = '{imei_no}'"
        cur.execute(isql)
        dev_data = cur.fetchall()
        
        if len(dev_data) > 0:
            new_device_no = dev_data[0]['board_no']
        else:
            # Insert new device
            isql = f"INSERT INTO gateway_device_master (imei_no, datetime) VALUES ('{imei_no}', '{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}')"
            cur.execute(isql)
            conn.commit()
            
            # Get the inserted device
            isql = f"SELECT * FROM gateway_device_master WHERE imei_no = '{imei_no}'"
            cur.execute(isql)
            did = cur.fetchall()
            dev_no = did[0]['gid']
            
            # Generate device number with # prefix
            new_device_no = onboarding_service.generate_device_number(dev_no, '#')
            
            # Update device number
            isql = f"UPDATE gateway_device_master SET board_no = '{new_device_no}' WHERE imei_no = '{imei_no}'"
            cur.execute(isql)
            conn.commit()
        
        conn.close()
        
        # Get ThingsBoard token
        tb_user_data = onboarding_service.get_tb_user_data()
        tb_auth_token = TBConnection.get_tb_token(request, tb_user_data)
        
        if tb_auth_token.get('status') != 200:
            return JSONResponse(
                content={'error_code': '4001', 'message': 'Failed to authenticate with ThingsBoard'},
                status_code=500
            )
        
        token = tb_auth_token.get('token')
        
        # Create device in ThingsBoard
        tb_device = onboarding_service.create_tb_device(new_device_no, "gw", token)
        
        if 'error' in tb_device:
            return JSONResponse(
                content={'error_code': '4003', 'message': tb_device['error']},
                status_code=500
            )
        
        tb_device_id = tb_device.get('id', {}).get('id')
        
        # Update device credentials
        credentials_result = onboarding_service.update_device_credentials(tb_device_id, imei_no, token)
        
        if 'error' in credentials_result:
            return JSONResponse(
                content={'error_code': '4004', 'message': credentials_result['error']},
                status_code=500
            )
        
        # Add server attributes
        attributes = {
            "state": "ONBOARDED",
            "condition": "NEW",
            "inactivityTimeout": 2100000,
            "boardNumber": new_device_no,
            "variant": variant
        }
        
        attr_result = onboarding_service.add_device_attributes(tb_device_id, attributes, token)
        
        if 'error' in attr_result:
            return JSONResponse(
                content={'error_code': '4005', 'message': attr_result['error']},
                status_code=500
            )
        
        # Update database with TB ID
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        isql = f"UPDATE gateway_device_master SET tb_production_id = '{tb_device_id}' WHERE imei_no = '{imei_no}'"
        cur.execute(isql)
        conn.commit()
        conn.close()
        
        logger.info(f"New gateway device created: {new_device_no}")
        return JSONResponse(content=new_device_no, status_code=200)
        
    except Exception as e:
        logger.exception(f"Failed to add gateway device: {e}")
        return JSONResponse(
            content={'error_code': '4009', 'message': str(e)},
            status_code=500
        )

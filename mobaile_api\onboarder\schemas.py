"""
Onboarder Pydantic schemas
Converted from Django onboarder models
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime


class DeviceOnboardResponse(BaseModel):
    """Response model for device onboarding"""
    device_no: Optional[str] = None
    error_code: Optional[str] = None
    message: Optional[str] = None


class ILMDeviceRequest(BaseModel):
    """Request model for ILM device onboarding"""
    credentials: str = Field(..., description="IEEE Address for ILM device")
    variant: str = Field(..., description="Device variant")


class GatewayDeviceRequest(BaseModel):
    """Request model for Gateway device onboarding"""
    imei_no: str = Field(..., description="IMEI number for gateway device")
    variant: str = Field(..., description="Device variant")


class ThingsBoardDevice(BaseModel):
    """ThingsBoard device model"""
    name: str
    type: str
    id: Optional[Dict[str, str]] = None


class ThingsBoardCredentials(BaseModel):
    """ThingsBoard device credentials model"""
    credentialsId: str
    credentialsValue: str
    credentialsType: str = "ACCESS_TOKEN"
    deviceId: Optional[Dict[str, str]] = None


class DeviceMaster(BaseModel):
    """Device master table model"""
    did: Optional[int] = None
    device_no: Optional[str] = None
    ieee_address: str
    tb_id: Optional[str] = None
    added_date: Optional[datetime] = None


class GatewayDeviceMaster(BaseModel):
    """Gateway device master table model"""
    gid: Optional[int] = None
    board_no: Optional[str] = None
    imei_no: str
    tb_production_id: Optional[str] = None
    datetime: Optional[datetime] = None

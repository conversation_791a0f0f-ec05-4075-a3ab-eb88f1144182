"""
Pydantic schemas for Onboarder module
"""
from typing import Optional, Dict, Any
from pydantic import BaseModel


class ILMDeviceOnboardRequest(BaseModel):
    credentials: str
    variant: str


class GatewayDeviceOnboardRequest(BaseModel):
    imei_no: str
    variant: str


class DeviceOnboardResponse(BaseModel):
    device_no: Optional[str] = None
    error_code: Optional[str] = None
    message: Optional[str] = None


class TBConnectionRequest(BaseModel):
    username: str
    password: str

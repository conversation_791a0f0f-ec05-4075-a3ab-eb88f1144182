"""
Service layer for Onboarder module
"""
import json
import logging
import requests
from typing import Dict, Any, Optional

from ..config import settings
from ..database import get_db_config

logger = logging.getLogger(__name__)


class TBConnection:
    """ThingsBoard connection service"""
    
    @staticmethod
    def get_tb_token(request, tb_user_data: str) -> Dict[str, Any]:
        """Get ThingsBoard token"""
        try:
            url = f"{settings.BASE_URL}/api/auth/login"
            headers = {'Content-type': 'application/json', 'Accept': '*/*'}
            
            response = requests.post(url=url, headers=headers, data=tb_user_data, timeout=60)
            
            if response.status_code == 200:
                result = json.loads(response.text)
                return {"token": result.get("token", ""), "status": 200}
            else:
                return {"error": "Authentication failed", "status": response.status_code}
                
        except Exception as e:
            logger.exception(f"Failed to get TB token: {e}")
            return {"error": str(e), "status": 500}
    
    @staticmethod
    def refresh_tb_token(request, api_url: str, method: str, tb_url: str, tb_user_data: str, data: str) -> requests.Response:
        """Refresh ThingsBoard token"""
        try:
            # Get new token
            token_response = TBConnection.get_tb_token(request, tb_user_data)
            
            if token_response.get("status") == 200:
                # Store token in session (for compatibility)
                if hasattr(request, 'session'):
                    request.session['tb_token'] = token_response["token"]
                
                # Make the original request with new token
                headers = {
                    'Content-type': 'application/json',
                    'Accept': '*/*',
                    'X-Authorization': f'Bearer {token_response["token"]}'
                }
                
                if method.lower() == 'get':
                    return requests.get(api_url, headers=headers, data=data, timeout=60)
                elif method.lower() == 'post':
                    return requests.post(api_url, headers=headers, data=data, timeout=60)
                else:
                    raise ValueError(f"Unsupported method: {method}")
            else:
                raise Exception("Failed to refresh token")
                
        except Exception as e:
            logger.exception(f"Failed to refresh TB token: {e}")
            raise


class DeviceOnboardingService:
    """Device onboarding service"""
    
    def __init__(self):
        self.db_config = get_db_config()
        
        # ILM variants profile mapping
        self.ilm_variants_profile_mapping = {
            "SLNNSGINSTDGX01A": ("ilm", 16),
            "SLNN4GQTLSTDG01A": ("ilm-4g", 15),
            "SLNN4GQTLDM0701A": ("ilm-4g", 15),
            "PILCNSGINPROG01A": ("ilm", 16),
        }
        
        # Device number prefix mapping
        self.dev_no_prefix = {
            i: chr(65 + i // 26) + chr(65 + i % 26) for i in range(101)
        }
        
        # Update with specific mappings
        self.dev_no_prefix.update({
            0: 'AA', 1: 'AB', 2: 'AC', 3: 'AD', 4: 'AE', 5: 'AF', 6: 'AG', 7: 'AH', 8: 'AI', 9: 'AJ',
            10: 'AK', 11: 'AL', 12: 'AM', 13: 'AN', 14: 'AO', 15: 'AP', 16: 'AQ', 17: 'AR', 18: 'AS',
            19: 'AT', 20: 'AU', 21: 'AV', 22: 'AW', 23: 'AX', 24: 'AY', 25: 'AZ', 26: 'BA', 27: 'BB',
            28: 'BC', 29: 'BD', 30: 'BE', 31: 'BF', 32: 'BG', 33: 'BH', 34: 'BI', 35: 'BJ', 36: 'BK',
            37: 'BL', 38: 'BM', 39: 'BN', 40: 'BO', 41: 'BP', 42: 'BQ', 43: 'BR', 44: 'BS', 45: 'BT',
            46: 'BU', 47: 'BV', 48: 'BW', 49: 'BX', 50: 'BY', 51: 'BZ', 52: 'CA', 53: 'CB', 54: 'CC',
            55: 'CD', 56: 'CE', 57: 'CF', 58: 'CG', 59: 'CH', 60: 'CI', 61: 'CJ', 62: 'CK', 63: 'CL',
            64: 'CM', 65: 'CN', 66: 'CO', 67: 'CP', 68: 'CQ', 69: 'CR', 70: 'CS', 71: 'CT', 72: 'CU',
            73: 'CV', 74: 'CW', 75: 'CX', 76: 'CY', 77: 'CZ', 78: 'DA', 79: 'DB', 80: 'DC', 81: 'DD',
            82: 'DE', 83: 'DF', 84: 'DG', 85: 'DH', 86: 'DI', 87: 'DJ', 88: 'DK', 89: 'DL', 90: 'DM',
            91: 'DN', 92: 'DO', 93: 'DP', 94: 'DQ', 95: 'DR', 96: 'DS', 97: 'DT', 98: 'DU', 99: 'DV',
            100: 'DW'
        })
    
    def generate_device_number(self, device_id: int, prefix: str = 'Z') -> str:
        """Generate device number from device ID"""
        dev_index_no = int(device_id / 1000)
        if dev_index_no:
            return f'{prefix}{self.dev_no_prefix[dev_index_no]}{str(device_id - (1000 * dev_index_no)).zfill(3)}'
        else:
            return f'{prefix}{self.dev_no_prefix[dev_index_no]}{str(device_id).zfill(3)}'
    
    def validate_ilm_credentials(self, credentials: str, variant: str) -> bool:
        """Validate ILM credentials"""
        device_profile_map = self.ilm_variants_profile_mapping.get(variant)
        if device_profile_map and len(credentials) == device_profile_map[1]:
            return True
        return False
    
    def get_tb_user_data(self) -> str:
        """Get ThingsBoard user data"""
        return json.dumps({
            "username": settings.TB_USERNAME,
            "password": settings.TB_PASSWORD
        })
    
    def create_tb_device(self, device_name: str, device_type: str, token: str) -> Dict[str, Any]:
        """Create device in ThingsBoard"""
        try:
            url = f"{settings.BASE_URL}/api/device"
            headers = {
                'Content-type': 'application/json',
                'Accept': '*/*',
                'X-Authorization': f'Bearer {token}'
            }
            data = {
                "name": device_name,
                "type": device_type
            }
            
            response = requests.post(url, headers=headers, data=json.dumps(data), timeout=60)
            
            if response.status_code == 200:
                return json.loads(response.text)
            else:
                logger.error(f"Failed to create TB device: {response.status_code} - {response.text}")
                return {"error": f"Failed to create device: {response.status_code}"}
                
        except Exception as e:
            logger.exception(f"Failed to create TB device: {e}")
            return {"error": str(e)}
    
    def update_device_credentials(self, device_id: str, credentials: str, token: str) -> Dict[str, Any]:
        """Update device credentials in ThingsBoard"""
        try:
            # Get current credentials
            url = f"{settings.BASE_URL}/api/device/{device_id}/credentials"
            headers = {
                'Content-type': 'application/json',
                'Accept': '*/*',
                'X-Authorization': f'Bearer {token}'
            }
            
            response = requests.get(url, headers=headers, timeout=60)
            
            if response.status_code == 200:
                credentials_data = json.loads(response.text)
                credentials_data['credentialsId'] = credentials
                credentials_data['credentialsValue'] = 'null'
                
                # Update credentials
                url = f"{settings.BASE_URL}/api/device/credentials"
                response = requests.post(url, headers=headers, data=json.dumps(credentials_data), timeout=60)
                
                if response.status_code == 200:
                    return json.loads(response.text)
                else:
                    logger.error(f"Failed to update credentials: {response.status_code} - {response.text}")
                    return {"error": f"Failed to update credentials: {response.status_code}"}
            else:
                logger.error(f"Failed to get credentials: {response.status_code} - {response.text}")
                return {"error": f"Failed to get credentials: {response.status_code}"}
                
        except Exception as e:
            logger.exception(f"Failed to update device credentials: {e}")
            return {"error": str(e)}
    
    def add_device_attributes(self, device_id: str, attributes: Dict[str, Any], token: str) -> Dict[str, Any]:
        """Add server attributes to device"""
        try:
            url = f"{settings.BASE_URL}/api/plugins/telemetry/DEVICE/{device_id}/SERVER_SCOPE"
            headers = {
                'Content-type': 'application/json',
                'Accept': '*/*',
                'X-Authorization': f'Bearer {token}'
            }
            
            response = requests.post(url, headers=headers, data=json.dumps(attributes), timeout=60)
            
            if response.status_code == 200:
                return {"status": "success"}
            else:
                logger.error(f"Failed to add attributes: {response.status_code} - {response.text}")
                return {"error": f"Failed to add attributes: {response.status_code}"}
                
        except Exception as e:
            logger.exception(f"Failed to add device attributes: {e}")
            return {"error": str(e)}

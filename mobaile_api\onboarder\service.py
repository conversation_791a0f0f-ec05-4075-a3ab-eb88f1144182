"""
Onboarder Service
Converted from Django onboarder/service.py
"""

import json
import logging
import requests
from typing import Dict, Any, Optional
from ..config import settings

logger = logging.getLogger(__name__)


class TBConnection:
    """ThingsBoard connection service"""
    
    @staticmethod
    def get_tb_token(login_credentials: Dict[str, str]) -> Dict[str, str]:
        """
        Get ThingsBoard authentication token
        """
        try:
            login_url = f"{settings.base_url}/api/auth/login"
            headers = {'Content-Type': 'application/json'}
            
            response = requests.post(
                login_url,
                headers=headers,
                data=json.dumps(login_credentials),
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get TB token: {response.status_code} - {response.text}")
                return {}
                
        except Exception as e:
            logger.exception(f"Error getting TB token: {e}")
            return {}
    
    @staticmethod
    def refresh_tb_token(api_url: str, method: str, tb_url: str, 
                        login_credentials: Dict[str, str], data: Any) -> requests.Response:
        """
        Refresh ThingsBoard token and retry request
        """
        try:
            # Get new token
            token_response = TBConnection.get_tb_token(login_credentials)
            new_token = token_response.get('token')
            
            if not new_token:
                raise Exception("Failed to refresh token")
            
            # Retry original request with new token
            headers = {
                'Content-type': 'application/json',
                'Accept': '*/*',
                'X-Authorization': f'Bearer {new_token}'
            }
            
            if method.lower() == 'get':
                return requests.get(api_url, headers=headers, data=data, timeout=60)
            elif method.lower() == 'post':
                return requests.post(api_url, headers=headers, data=json.dumps(data) if isinstance(data, dict) else data, timeout=60)
            else:
                raise Exception(f"Unsupported method: {method}")
                
        except Exception as e:
            logger.exception(f"Error refreshing TB token: {e}")
            raise


# Device number prefix mapping (from Django views)
dev_no_prefix = {
    0: 'AA', 1: 'AB', 2: 'AC', 3: 'AD', 4: 'AE', 5: 'AF', 6: 'AG', 7: 'AH', 8: 'AI', 9: 'AJ',
    10: 'AK', 11: 'AL', 12: 'AM', 13: 'AN', 14: 'AO', 15: 'AP', 16: 'AQ', 17: 'AR', 18: 'AS',
    19: 'AT', 20: 'AU', 21: 'AV', 22: 'AW', 23: 'AX', 24: 'AY', 25: 'AZ', 26: 'BA', 27: 'BB',
    28: 'BC', 29: 'BD', 30: 'BE', 31: 'BF', 32: 'BG', 33: 'BH', 34: 'BI', 35: 'BJ', 36: 'BK',
    37: 'BL', 38: 'BM', 39: 'BN', 40: 'BO', 41: 'BP', 42: 'BQ', 43: 'BR', 44: 'BS', 45: 'BT',
    46: 'BU', 47: 'BV', 48: 'BW', 49: 'BX', 50: 'BY', 51: 'BZ', 52: 'CA', 53: 'CB', 54: 'CC',
    55: 'CD', 56: 'CE', 57: 'CF', 58: 'CG', 59: 'CH', 60: 'CI', 61: 'CJ', 62: 'CK', 63: 'CL',
    64: 'CM', 65: 'CN', 66: 'CO', 67: 'CP', 68: 'CQ', 69: 'CR', 70: 'CS', 71: 'CT', 72: 'CU',
    73: 'CV', 74: 'CW', 75: 'CX', 76: 'CY', 77: 'CZ', 78: 'DA', 79: 'DB', 80: 'DC', 81: 'DD',
    82: 'DE', 83: 'DF', 84: 'DG', 85: 'DH', 86: 'DI', 87: 'DJ', 88: 'DK', 89: 'DL', 90: 'DM',
    91: 'DN', 92: 'DO', 93: 'DP', 94: 'DQ', 95: 'DR', 96: 'DS', 97: 'DT', 98: 'DU', 99: 'DV',
    100: 'DW'
}

# ILM variants profile mapping
ILM_VARIANTS_PROFILE_MAPPING = {
    "SLNNSGINSTDGX01A": ("ilm", 16),
    "SLNN4GQTLSTDG01A": ("ilm-4g", 15),
    "SLNN4GQTLDM0701A": ("ilm-4g", 15),
    "PILCNSGINPROG01A": ("ilm", 16),
}


def generate_device_number(dev_no: int, prefix: str = 'Z') -> str:
    """Generate device number based on device ID"""
    dev_index_no = int(dev_no / 1000)
    if dev_index_no:
        return f'{prefix}{dev_no_prefix[dev_index_no]}{str(dev_no - (1000 * dev_index_no)).zfill(3)}'
    else:
        return f'{prefix}{dev_no_prefix[dev_index_no]}{str(dev_no).zfill(3)}'

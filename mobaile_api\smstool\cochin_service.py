"""
Cochin Service for CSML integration
Extracted from Django patch_views.py
"""

import json
import logging
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import PlainTextResponse
import requests
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/cochin", tags=["Cochin CSML"])

# Cochin user data - should be moved to config
kochin_user_data = {
    "username": "your_cochin_username",
    "password": "your_cochin_password"
}


def cochin_login():
    """
    Login to Cochin CSML system
    Converted from Django patch_views.cochin_login
    """
    try:
        logger.debug("CSML data forwarding - Login...")
        header = {'Content-Type': 'application/x-www-form-urlencoded'}
        
        if not settings.csml_login_url:
            raise Exception("CSML_LOGIN_URL not configured")
            
        response = requests.post(
            settings.csml_login_url, 
            headers=header, 
            data=kochin_user_data, 
            timeout=140, 
            verify=False
        )
        result = json.loads(response.text)
        return result['access_token']
        
    except Exception as e:
        logger.exception(f"Cochin customer login failed: {e}")
        return None


@router.post("/lamp/failure/")
async def lamp_failure_details(request: Request):
    """
    Handle lamp failure details for Cochin CSML
    Converted from Django patch_views.lamp_failure_details
    """
    try:
        request_body = await request.body()
        request_params = json.loads(request_body)
        
        header = {'Content-Type': 'application/json'}
        auth_token = cochin_login()
        
        if not auth_token:
            raise HTTPException(status_code=401, detail="Failed to authenticate with CSML")
            
        header['Authorization'] = f'bearer {auth_token}'
        
        if not settings.csml_lamp_failure_url:
            raise HTTPException(status_code=500, detail="CSML_LAMP_FAILURE_URL not configured")
            
        logger.debug(f"CSML data forwarding - Lamp Failure Report: {settings.csml_lamp_failure_url}, {request_params}")
        
        response = requests.post(
            url=settings.csml_lamp_failure_url,
            headers=header,
            data=json.dumps(request_params),
            timeout=140,
            verify=False
        )
        
        logger.debug(f"CSML data forwarding - success: {response.text}")
        response.raise_for_status()
        
        return PlainTextResponse(content="ok", status_code=200)
        
    except requests.exceptions.RequestException as e:
        logger.exception(f"CSML data forwarding - failure: {e}")
        raise HTTPException(status_code=500, detail=f"CSML request failed: {str(e)}")
    except Exception as e:
        logger.exception(f"CSML data forwarding - failure: {e}")
        raise HTTPException(status_code=500, detail=str(e))

"""
Cochin service for lamp failure data forwarding
"""
import json
import logging
import requests
from typing import Dict, Any

from ..config import settings, CSML_LOGIN_URL, CSML_LAMP_FAILURE_URL, kochin_user_data

logger = logging.getLogger(__name__)


class CochinService:
    """Service for handling Cochin CSML data forwarding"""
    
    def __init__(self):
        self.login_url = CSML_LOGIN_URL
        self.lamp_failure_url = CSML_LAMP_FAILURE_URL
        self.user_data = kochin_user_data
        self._access_token = None
    
    async def get_access_token(self) -> str:
        """Get access token for Cochin CSML API"""
        try:
            logger.debug("CSML data forwarding - Login...")
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            response = requests.post(
                self.login_url,
                headers=headers,
                data=self.user_data,
                timeout=140,
                verify=False
            )
            
            if response.status_code == 200:
                result = json.loads(response.text)
                access_token = result.get('access_token')
                
                if access_token:
                    self._access_token = access_token
                    logger.debug("CSML login successful")
                    return access_token
                else:
                    logger.error("No access token in response")
                    return None
            else:
                logger.error(f"CSML login failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.exception(f"Cochin customer login failed: {e}")
            return None
    
    async def send_lamp_failure_data(self, failure_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send lamp failure data to Cochin CSML API"""
        try:
            # Get access token
            access_token = await self.get_access_token()
            
            if not access_token:
                return {"success": False, "error": "Failed to get access token"}
            
            # Prepare headers
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'bearer {access_token}'
            }
            
            logger.debug(f"CSML data forwarding - Lamp Failure Report: {self.lamp_failure_url}")
            logger.debug(f"CSML data forwarding - Request data: {failure_data}")
            
            # Send lamp failure data
            response = requests.post(
                url=self.lamp_failure_url,
                headers=headers,
                data=json.dumps(failure_data),
                timeout=140,
                verify=False
            )
            
            # Check response
            response.raise_for_status()
            
            response_data = json.loads(response.text)
            logger.debug(f"CSML data forwarding - success: {response_data}")
            
            return {"success": True, "response": response_data}
            
        except requests.exceptions.RequestException as e:
            logger.exception(f"CSML data forwarding - HTTP error: {e}")
            return {"success": False, "error": f"HTTP error: {str(e)}"}
        except json.JSONDecodeError as e:
            logger.exception(f"CSML data forwarding - JSON decode error: {e}")
            return {"success": False, "error": f"JSON decode error: {str(e)}"}
        except Exception as e:
            logger.exception(f"CSML data forwarding - failure: {e}")
            return {"success": False, "error": str(e)}
    
    def validate_failure_data(self, failure_data: Dict[str, Any]) -> bool:
        """Validate lamp failure data"""
        required_fields = ['deviceId', 'timestamp', 'failureType']
        
        for field in required_fields:
            if field not in failure_data:
                logger.warning(f"Missing required field: {field}")
                return False
        
        return True
    
    async def send_bulk_failure_data(self, failure_data_list: list) -> Dict[str, Any]:
        """Send bulk lamp failure data"""
        try:
            results = []
            
            for failure_data in failure_data_list:
                if self.validate_failure_data(failure_data):
                    result = await self.send_lamp_failure_data(failure_data)
                    results.append(result)
                else:
                    results.append({
                        "success": False,
                        "error": "Invalid failure data",
                        "data": failure_data
                    })
            
            success_count = sum(1 for r in results if r.get("success"))
            total_count = len(results)
            
            return {
                "success": success_count > 0,
                "total": total_count,
                "success_count": success_count,
                "failed_count": total_count - success_count,
                "results": results
            }
            
        except Exception as e:
            logger.exception(f"Bulk failure data sending failed: {e}")
            return {"success": False, "error": str(e)}

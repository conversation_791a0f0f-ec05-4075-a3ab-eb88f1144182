"""
SMS Tool Router - FastAPI routes for SMS and external services
Extracted from Django lume/patch_views.py
"""

import json
import logging
import urllib.parse
from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import PlainTextResponse
import requests
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/sms", tags=["SMS Tool"])


@router.get("/send/")
async def smart_lights_sms(
    uname: str,
    password: str = None,
    dest: str = None,
    msg: str = None
):
    """
    Send SMS on behalf of SmartLights
    Converted from Django patch_views.smart_lights_sms
    """
    try:
        if not all([uname, password, dest, msg]):
            raise HTTPException(status_code=400, detail="Missing required parameters")
            
        # URL encode the message
        encoded_msg = urllib.parse.urlencode({"msg": msg})
        request_url = f'https://api.instaalerts.zone/SendSMS/sendmsg.php?uname={uname}&pass={password}&send=SCHNEL&dest={dest}&{encoded_msg}'
        
        logger.debug(f"[SMS-REQ] On behalf of SmartLights: {request_url}")
        response = requests.post(url=request_url)
        logger.debug(f"[SMS-RES] On behalf of SmartLights: {response.text}")
        
        return PlainTextResponse(content=response.text, status_code=response.status_code)
        
    except Exception as e:
        logger.exception(f"SMS sending failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ghmc/tran_ghmc/")
async def ghmc_transact(request: Request):
    """
    GHMC transaction handler
    Converted from Django patch_views.ghmc_transact
    """
    try:
        headers = {'Content-type': 'application/json', 'Accept': 'text/plain'}
        tr_data = await request.body()
        tr_data_str = tr_data.decode('utf-8')
        
        transact_url = "https://ghmcmobileapp.ghmc.gov.in/StreetLights/Insert_Streetlight_Data_ArrayList"
        logger.debug(f"[GHMC] On behalf of SmartLights: {transact_url}")
        
        response = requests.post(url=transact_url, headers=headers, data=tr_data_str, timeout=1200)
        logger.debug(f"[GHMC] On behalf of SmartLights: {response.text}")
        
        return PlainTextResponse(content=response.text, status_code=response.status_code)
        
    except Exception as e:
        logger.exception(f"GHMC transaction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ghmc/tran_ap/")
async def ap_transact(request: Request):
    """
    AP transaction handler
    Converted from Django patch_views.ap_transact
    """
    try:
        headers = {'Content-type': 'application/json', 'Accept': 'text/plain'}
        tr_data = await request.body()
        tr_data_str = tr_data.decode('utf-8')
        
        transact_url = "http://webservice.apccms.in/pan/panchayatservice.php"
        logger.debug(f"[AP] On behalf of SmartLights: {transact_url}")
        
        response = requests.post(url=transact_url, headers=headers, data=tr_data_str, timeout=1200)
        logger.debug(f"[AP] On behalf of SmartLights: {response.text}")
        
        return PlainTextResponse(content=response.text, status_code=response.status_code)
        
    except Exception as e:
        logger.exception(f"AP transaction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

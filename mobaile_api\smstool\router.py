"""
FastAPI router for SMS Tool module - SMS and external service endpoints
"""
import json
import logging
from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, Query
from fastapi.responses import JSONResponse, PlainTextResponse

from .cochin_service import CochinService
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/sms/send/")
async def smart_lights_sms(
    request: Request,
    uname: str = Query(..., description="Username"),
    password: str = Query(..., alias="pass", description="Password"),
    dest: str = Query(..., description="Destination phone number"),
    msg: str = Query(..., description="Message content")
):
    """Send SMS on behalf of SmartLights"""
    try:
        import urllib.parse
        import requests
        
        # Encode message
        encoded_msg = urllib.parse.urlencode({"msg": msg})
        
        # Construct request URL
        request_url = f'https://api.instaalerts.zone/SendSMS/sendmsg.php?uname={uname}&pass={password}&send=SCHNEL&dest={dest}&{encoded_msg}'
        
        logger.debug(f"[SMS-REQ] On behalf of SmartLights: {request_url}")
        
        # Send SMS request
        response = requests.post(url=request_url, timeout=30)
        
        logger.debug(f"[SMS-RES] On behalf of SmartLights: {response.text}")
        
        return PlainTextResponse(content=response.text, status_code=response.status_code)
        
    except Exception as e:
        logger.exception(f"SMS sending failed: {e}")
        return PlainTextResponse(content=str(e), status_code=500)


@router.post("/ghmc/tran_ghmc/")
async def ghmc_transact(request: Request):
    """GHMC transaction endpoint"""
    try:
        import requests
        
        # Get request body
        body = await request.body()
        tr_data = body.decode('utf-8')
        
        # GHMC API endpoint
        transact_url = "https://ghmcmobileapp.ghmc.gov.in/StreetLights/Insert_Streetlight_Data_ArrayList"
        
        headers = {
            'Content-type': 'application/json',
            'Accept': 'text/plain'
        }
        
        logger.debug(f"[GHMC] On behalf of SmartLights: {transact_url}")
        logger.debug(f"[GHMC] Request data: {tr_data}")
        
        # Forward request to GHMC
        response = requests.post(
            url=transact_url,
            headers=headers,
            data=tr_data,
            timeout=1200
        )
        
        logger.debug(f"[GHMC] Response: {response.text}")
        
        return PlainTextResponse(content=response.text, status_code=response.status_code)
        
    except Exception as e:
        logger.exception(f"GHMC transaction failed: {e}")
        return PlainTextResponse(content=str(e), status_code=500)


@router.post("/ghmc/tran_ap/")
async def ap_transact(request: Request):
    """AP transaction endpoint"""
    try:
        import requests
        
        # Get request body
        body = await request.body()
        tr_data = body.decode('utf-8')
        
        # AP API endpoint
        transact_url = "http://webservice.apccms.in/pan/panchayatservice.php"
        
        headers = {
            'Content-type': 'application/json',
            'Accept': 'text/plain'
        }
        
        logger.debug(f"[AP] On behalf of SmartLights: {transact_url}")
        logger.debug(f"[AP] Request data: {tr_data}")
        
        # Forward request to AP
        response = requests.post(
            url=transact_url,
            headers=headers,
            data=tr_data,
            timeout=1200
        )
        
        logger.debug(f"[AP] Response: {response.text}")
        
        return PlainTextResponse(content=response.text, status_code=response.status_code)
        
    except Exception as e:
        logger.exception(f"AP transaction failed: {e}")
        return PlainTextResponse(content=str(e), status_code=500)


@router.post("/cochin/lamp/failure/")
async def lamp_failure_details(request: Request):
    """Cochin lamp failure details endpoint"""
    try:
        # Get request body
        body = await request.body()
        request_params = json.loads(body)
        
        # Use Cochin service to handle the request
        cochin_service = CochinService()
        result = await cochin_service.send_lamp_failure_data(request_params)
        
        if result.get("success"):
            return PlainTextResponse(content="ok", status_code=200)
        else:
            logger.error(f"Cochin lamp failure failed: {result.get('error')}")
            return PlainTextResponse(content="error", status_code=500)
        
    except Exception as e:
        logger.exception(f"Cochin lamp failure processing failed: {e}")
        return PlainTextResponse(content="error", status_code=500)

# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
pymysql==1.1.0
cryptography==41.0.7

# Pydantic for data validation
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP requests
requests==2.31.0
httpx==0.25.2

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# AWS SDK
boto3==1.34.0
botocore==1.34.0

# Google Cloud
google-cloud-pubsub==2.18.4
google-cloud-storage==2.10.0

# Cryptography
pycryptodome==3.19.0

# Date and time handling
python-dateutil==2.8.2

# Environment variables
python-dotenv==1.0.0

# Logging
structlog==23.2.0

# Image processing
Pillow==10.1.0

# JSON handling
orjson==3.9.10

# CORS
fastapi-cors==0.0.6

# Testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development tools (optional)
black==23.11.0
isort==5.12.0
flake8==6.1.0

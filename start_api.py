#!/usr/bin/env python3
"""
Startup script for SCHNELLIOT FastAPI application
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def check_virtual_environment():
    """Check if running in virtual environment"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Running in virtual environment")
        return True
    else:
        print("⚠️  Not running in virtual environment")
        print("Recommendation: Create and activate a virtual environment")
        return True  # Don't fail, just warn

def check_requirements():
    """Check if requirements are installed"""
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import pymysql
        import pydantic
        print("✅ Core requirements are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing requirement: {e}")
        print("Please install requirements: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists"""
    env_path = Path("mobaile_api/.env")
    if env_path.exists():
        print("✅ Environment file found")
        return True
    else:
        print("⚠️  Environment file not found")
        print("Creating example .env file...")
        create_example_env()
        return True

def create_example_env():
    """Create example .env file"""
    env_content = """# SCHNELLIOT API Configuration
DEBUG=True
SECRET_KEY=your-secret-key-here-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/schnelliot_db
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=schnelliot_db

# ThingsBoard Configuration
BASE_URL=https://your-thingsboard-url
LOGIN_CREDENTIALS={"username": "your-username", "password": "your-password"}

# SMS Services Configuration
COCHIN_CSML_USERNAME=your-cochin-username
COCHIN_CSML_PASSWORD=your-cochin-password
COCHIN_CSML_URL=https://your-cochin-url
GHMC_SMS_URL=https://your-ghmc-sms-url
GHMC_SMS_USERNAME=your-ghmc-username
GHMC_SMS_PASSWORD=your-ghmc-password

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-s3-bucket

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-gcp-project
PUBSUB_TOPIC=your-pubsub-topic
"""
    
    os.makedirs("mobaile_api", exist_ok=True)
    with open("mobaile_api/.env", "w") as f:
        f.write(env_content)
    print("📝 Created mobaile_api/.env file - please update with your configuration")

def start_server():
    """Start the FastAPI server"""
    print("🚀 Starting SCHNELLIOT API server...")
    print("Server will be available at: http://localhost:8000")
    print("API Documentation: http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Start uvicorn server
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "mobaile_api.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return False
    
    return True

def main():
    """Main startup function"""
    print("SCHNELLIOT API Startup Script")
    print("=" * 50)
    
    # Run checks
    checks = [
        ("Python Version", check_python_version),
        ("Virtual Environment", check_virtual_environment),
        ("Requirements", check_requirements),
        ("Environment File", check_env_file),
    ]
    
    for check_name, check_func in checks:
        print(f"\n🔍 Checking {check_name}...")
        if not check_func():
            print(f"❌ {check_name} check failed")
            sys.exit(1)
    
    print("\n✅ All checks passed!")
    print("\n" + "=" * 50)
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()

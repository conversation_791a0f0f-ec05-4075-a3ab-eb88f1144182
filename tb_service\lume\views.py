from lume.service import *
from tb_service.settings import *
from lume.__init__ import *
# from utils.utils import upload_image_to_aws
from utils.utils import UtilService

@method_validate("POST")
def login(request):
    request_handler = RequestResponseHandler()
    try:
        _username = request.POST.get('username')
        _password = request.POST.get('password')
        _ts = request.POST.get('ts')
        latitude = request.POST.get('latitude')
        longitude = request.POST.get('longitude')
        logger.info("User Login: %s" % _username)
        if not _username or not _password :
            raise request_handler.update_error_response("Username/Password is required.")
        attendance_service = AttendanceService()
        attendance_service.handle_login(
            _username=_username, _password=_password,
            request_handler=request_handler, _ts=_ts,
            latitude=latitude, longitude=longitude
        )
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Failed to login %s" % str(e))
    return HttpResponse(request_handler.get_service_response())

@method_validate("POST")
def add_iam_user(request):
    request_handler = RequestResponseHandler()
    try:
        customer_name = request.POST.get('CustomerName')
        region = request.POST.get('region')
        user_email_id = request.POST.get('userEmailId')
        attendance_service = AttendanceService()
        attendance_service.publish_iam_user_data_to_pubsub(message={"CustomerName": customer_name, 
                                                                    "region": region,
                                                                    "userEmailId": user_email_id
                                                                    })
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Failed to login %s" % str(e))
    return HttpResponse(request_handler.get_service_response())

@token_validator
@method_validate("POST")
def add_iam_user(request):
    request_handler = RequestResponseHandler()
    try:
        customer_name = request.POST.get('customer_name')
        region = request.POST.get('region')
        user_email_id = request.POST.get('user_email_id')
        if not customer_name :
            raise request_handler.update_error_response("customer_name is required.")
        if not region :
            raise request_handler.update_error_response("region is required.")
        if not user_email_id :
            raise request_handler.update_error_response("user_email_id is required.")

        attendance_service = AttendanceService()
        attendance_service.publish_user_data_to_pubsub(
                                                        customer_name=customer_name, 
                                                        region=region,
                                                        user_email_id=user_email_id
                                                      )
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Failed to login %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def get_current_user_role(request):
    try:
        response = {True: {"status": 200}, False: {"status": 404}}
        request_handler = RequestResponseHandler()
        auth_service = TbAuthService(instance=request_handler)
        roles = auth_service.get_user_permissions(header=request.headers.get("token"))
        return HttpResponse(json.dumps(response[auth_service.get_roles(roles)]))
    except Exception as e:
        logger.exception("Couldn't get user role %s" % str(e))
    return None


@token_validator
@method_validate("POST")
def logout(request):
    request_handler = RequestResponseHandler()
    try:
        token = request.headers.get("token")
        _ts = request.POST.get('ts')
        latitude = request.POST.get('latitude')
        longitude = request.POST.get('longitude')
        auth_service = TbAuthService(instance=request_handler)
        user_info = auth_service.get_user_details(header=token)
        _username = user_info.get("name")
        attendance_service = AttendanceService()
        attendance_service.handle_logout(_token=token, request_handler=request_handler, _username=_username, _ts=_ts, latitude=latitude, longitude=longitude)
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Failed to logout %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def get_device_details(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        device_query_param = request_params.get('device')
        device_details = device_service.get_entity_by_name(entity_type="device", name=device_query_param)
        device_id = device_details.get('id').get('id')
        get_relation_info = []
        attribute_search_keys = GW_SERVER_ATTRIBUTES + "," + COMMON_PARAMS
        _device_profile = device_details.get("type").lower()
        if _device_profile in (DEVICE_TYPES[1], DEVICE_TYPES[2], DEVICE_TYPES[4]):
            get_relation_info = device_service.get_entity_to_relation_info(to_id=device_id,
                                                                           to_type=ENTITY_TYPE[0])
            attribute_search_keys = ILM_SERVER_ATTRIBUTES + "," + COMMON_PARAMS
        elif _device_profile in (DEVICE_TYPES[0], DEVICE_TYPES[3], DEVICE_TYPES[4], DEVICE_TYPES[5]):
            to_relation_query = device_service.gw_device_relations(
                entity_id=device_id, entity_type=ENTITY_TYPE[0], direction=ENTITY_RELATIONS[1],
                relation_type=RELATION_TYPES[1])
            to_relation_detail = device_service.find_entities_by_query(query=to_relation_query)
            get_relation_info = device_service.relation_construct(data=to_relation_detail["data"], attributes=True)
        get_attributes = device_service.get_entity_attribute(
            entity_type=ENTITY_TYPE[0], entity_id=device_id, scope=ATTRIBUTE_SCOPES[1], keys=attribute_search_keys)
        request_handler.update_service_response(device_service.get_lume_ilm_packet(
            device_details, get_attributes, get_relation_info, device_details.get('type')))
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't get device details %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def ilm_test_initiate_and_reset(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        attribute_json = device_service.ilm_test_attributes()
        device_id = request_params.get(ILM_TEST_QUERY_PARAMS[1])
        get_test_details = device_service.validate_test_initiate(request_params, attribute_json)
        if get_test_details:
            jig_name = request_params.get(ILM_TEST_QUERY_PARAMS[0])
            asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
            asset_details = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1],
                                                             name=jig_name)
            if asset_details.get("type") == ILM_ASSET_TYPES[1]:
                asset_from_relation_details = asset_service.get_entity_from_relation(
                    from_id=asset_details.get("id").get("id"), from_type=ENTITY_TYPE[1])
                asset_service.remove_asset_relations(asset_relations=asset_from_relation_details,
                                                     related_id=asset_details.get("id").get("id"),
                                                     related_type=ENTITY_TYPE[1],
                                                     removal_type=ENTITY_RELATIONS[1].lower())
                device_service.get_and_remove_to_relations(to_id=device_id, to_type=ENTITY_TYPE[0],
                                                           removal_type=ENTITY_RELATIONS[0].lower(), removal=True)
                asset_service.create_entity_relation(from_id=asset_details.get("id").get("id"),
                                                     from_type=ENTITY_TYPE[1],
                                                     to_id=device_id, to_type=ENTITY_TYPE[0],
                                                     relation_type=RELATION_TYPES[1])
                get_test_details["testResults"]["jig_number"] = jig_name
                device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0], entity_id=device_id,
                                                       scope=ATTRIBUTE_SCOPES[1], data=json.dumps(get_test_details))
        else:
            request_handler.update_service_response({"status": 1000,
                                                     "message": "jigNumer or action not present in request"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("ilm testing process not completed %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def update_ilm_attribute_details(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        device_id = request_params.get(ILM_TEST_QUERY_PARAMS[1])
        get_attributes = device_service.construct_attributes(request_attributes=request_params,
                                                             invalid_keys=ILM_TEST_QUERY_PARAMS)
        if request_params.get("state").lower() == AVAILABLE_STATES[0].lower():
            device_service.get_and_remove_to_relations(to_id=device_id, to_type=ENTITY_TYPE[0],
                                                       removal_type=ENTITY_RELATIONS[0].lower(), removal=True)
        if request_params.get("condition").lower() == AVAILABLE_CONDITION[2].lower():
            device_service.get_and_remove_from_and_to_relations(entity_id=device_id, entity_type=ENTITY_TYPE[0])
        device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0], entity_id=device_id,
                                               scope=ATTRIBUTE_SCOPES[1], data=json.dumps(get_attributes))
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't update ilm attribute details %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def dispatch_gw(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        auth_token = request.headers.get('token')
        device_service = DeviceService(token=auth_token, instance=request_handler)
        device_id = request_params.get(ILM_TEST_QUERY_PARAMS[1])
        get_device_name = device_service.get_entity_by_id(entity_type=ENTITY_TYPE[0], entity_id=device_id)
        device_name = get_device_name.get("name")
        device_attributes = device_service.construct_attributes(request_attributes=request_params,
                                                                invalid_keys=GW_DISPATCH_QUERY_PARAMS[
                                                                             0:3] + GW_ENTITY_ID_KEY)
        device_service.get_and_remove_to_relations(to_id=device_id, to_type=ENTITY_TYPE[0],
                                                   removal_type=ENTITY_RELATIONS[0].lower(), removal=True)
        if request_params.get(GW_DISPATCH_QUERY_PARAMS[1]).lower() == "gw":
            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0], entity_id=device_id,
                                                   scope=ATTRIBUTE_SCOPES[1], data=json.dumps(device_attributes))
        else:
            if request_params.get(GW_DISPATCH_QUERY_PARAMS[1]).lower() == GW_ASSET_TYPES[1]:
                asset_attributes = device_service.construct_attributes(request_attributes=request_params,
                                                                       invalid_keys=GW_DISPATCH_QUERY_PARAMS[
                                                                                    0:4] + GW_ENTITY_ID_KEY)
            else:
                asset_attributes = device_service.construct_attributes(request_attributes=request_params,
                                                                       invalid_keys=GW_DISPATCH_QUERY_PARAMS[
                                                                                    0:2] + GW_ENTITY_ID_KEY)
            asset_service = AssetService(token=auth_token, instance=request_handler)
            new_asset_detail = asset_service.construct_asset(request_param=request_params, label=device_name)
            if new_asset_detail:
                asset_detail = asset_service.create_or_update_entity(entity_type=ENTITY_TYPE[1],
                                                                     data=new_asset_detail)
                updated_ccms_detail = asset_service.validate_and_update_ccms(asset_detail=asset_detail,
                                                                             device_name=device_name)
                panel_id, ccms_name = updated_ccms_detail.get("id"), updated_ccms_detail.get("name")
                relation_detail = asset_service.create_entity_relation(
                    from_id=panel_id, from_type=ENTITY_TYPE[1], to_id=device_id,
                    to_type=ENTITY_TYPE[0], relation_type=RELATION_TYPES[1])
                uid_attribute = {"uid": ccms_name, "baselineOnTime": 43200}
                get_device_name["label"] = ccms_name
                device_service.create_device(entity_type=ENTITY_TYPE[0], data=get_device_name)
                asset_attributes.update(uid_attribute)
                if relation_detail.get('status') == 200:
                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                           entity_id=panel_id,
                                                           scope=ATTRIBUTE_SCOPES[1],
                                                           data=json.dumps(asset_attributes))
                    device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0], entity_id=device_id,
                                                           scope=ATTRIBUTE_SCOPES[1],
                                                           data=json.dumps(device_attributes))
            else:
                request_handler.update_service_response({"status": 1000, "message": "Error While Create CCMS/GW Asset"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't dispatch gw device with asset %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def update_gw_attribute_details(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        device_id = request_params.get(ILM_TEST_QUERY_PARAMS[1])
        get_attributes = device_service.construct_attributes(request_attributes=request_params,
                                                             invalid_keys=GW_DISPATCH_QUERY_PARAMS + GW_ENTITY_ID_KEY)
        if request_params.get("state").lower() == AVAILABLE_STATES[0].lower():
            device_service.get_and_remove_to_relations(to_id=device_id, to_type=ENTITY_TYPE[0],
                                                       removal_type=ENTITY_RELATIONS[0].lower(), removal=True)
        if request_params.get("condition").lower() == AVAILABLE_CONDITION[2].lower():
            device_service.get_and_remove_from_and_to_relations(entity_id=device_id, entity_type=ENTITY_TYPE[0])
        device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0], entity_id=device_id,
                                               scope=ATTRIBUTE_SCOPES[1], data=json.dumps(get_attributes))
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't update gw device attributes %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@method_validate("POST")
def device_take_service(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        device_id = request_params.get(ILM_TEST_QUERY_PARAMS[1])
        req_condition = request_params.get(GW_INSTALLATION_ATTRIBUTES[1])
        get_attributes = device_service.construct_attributes(request_attributes=request_params,
                                                             invalid_keys=GW_DISPATCH_QUERY_PARAMS + GW_ENTITY_ID_KEY)
        if req_condition == AVAILABLE_CONDITION[1]:
            device_service.get_and_remove_from_and_to_relations(entity_id=device_id, entity_type=ENTITY_TYPE[0])
            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0], entity_id=device_id,
                                                   scope=ATTRIBUTE_SCOPES[1], data=json.dumps(get_attributes))
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Device couldn't take in service %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_customers(request):
    request_handler = RequestResponseHandler()
    try:
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        customer_service = CustomerService(token=request.headers.get('token'), instance=request_handler)
        auth_service = TbAuthService(instance=request_handler)
        user_info = auth_service.get_user_details(header=request.headers.get("token"))
        user_id = user_info.get("id").get("id")
        customer_query = customer_service.customer_relations(
            entity_id=user_id, entity_type=OWNER_TYPE[2], direction=ENTITY_RELATIONS[0],
            relation_type=RELATION_TYPES[6])
        relation_details = asset_service.find_entities_by_query(query=customer_query)
        if relation_details["data"]:
            request_handler.update_service_response(asset_service.construct_customer_details(
                data=relation_details.get("data"), tenant_admin=False))
        else:
            customer_details = customer_service.get_all_customers()
            request_handler.update_service_response(asset_service.construct_customer_details(
                data=customer_details.get("data"), tenant_admin=True))
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't get region details %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_regions(request):
    request_handler = RequestResponseHandler()
    try:
        if request.GET.get("customerId"):
            customer_id = request.GET.get("customerId")
            asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
            device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
            auth_service = TbAuthService(instance=request_handler)
            # customer_service = CustomerService(token=request.headers.get('token'), instance=request_handler)
            user_info = auth_service.get_user_details(header=request.headers.get("token"))
            user_id = user_info.get("id").get("id")
            to_relation_query = device_service.device_relations(entity_id=user_id, entity_type=OWNER_TYPE[2],
                                                                direction=ENTITY_RELATIONS[0],
                                                                relation_type=RELATION_TYPES[6])
            relation_details = device_service.find_entities_by_query(query=to_relation_query)
            if relation_details.get("data"):
                request_handler.update_service_response(asset_service.construct_region_details_asset(
                    data=relation_details.get("data"), tenant_admin=False, customer_id=customer_id))
            else:
                # region_details = customer_service.get_all_customer_assets(customer_id=request.GET.get("customerId"),
                #                                                           asset_type=ILM_ASSET_TYPES[2])
                relation_query = asset_service.asset_level_entity(entity_id=customer_id, asset_type=[ILM_ASSET_TYPES[2]],
                                                 relation_type=RELATION_TYPES[0], entity_type=OWNER_TYPE[0], max_level=1)
                region_details = asset_service.find_entities_by_query(query=relation_query)
                request_handler.update_service_response(asset_service.construct_region_details_asset(
                    data=region_details.get("data"), tenant_admin=True))
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't get region details %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_zones(request):
    request_handler = RequestResponseHandler()
    try:
        if request.GET.get("regionId"):
            region_id = request.GET.get("regionId")
            asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
            zone_query = asset_service.asset_level_entity(entity_id=str(region_id), asset_type=[ILM_ASSET_TYPES[3]],
                                                          relation_type=RELATION_TYPES[0], max_level=1,
                                                          entity_type=ENTITY_TYPE[1])
            zone_details = asset_service.asset_related_asset(query=zone_query)
            request_handler.update_service_response(asset_service.construct_asset_details(data=zone_details.get('data'),
                                                                                          asset_type="zones"))
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't get zone details %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_wards(request):
    request_handler = RequestResponseHandler()
    try:
        zone_id = request.GET.get("zoneId")
        if request.GET.get("zoneId"):
            asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
            ward_query = asset_service.asset_lat_lon(entity_id=str(zone_id), entity_type=ENTITY_TYPE[1],
                                                     asset_type=[ILM_ASSET_TYPES[4]], relation_type=RELATION_TYPES[0])
            ward_details = asset_service.asset_related_asset(query=ward_query)
            request_handler.update_service_response(asset_service.construct_asset_details(data=ward_details.get("data"),
                                                                                          asset_type="wards"))
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't get ward details %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_entities(request):
    request_handler = RequestResponseHandler()
    try:
        ward_id = request.GET.get('wardId')
        if request.GET.get('wardId'):
            asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
            asset_query = asset_service.asset_level_entity(
                entity_id=str(ward_id), entity_type=ENTITY_TYPE[1], max_level=1, relation_type=RELATION_TYPES[0],
                asset_type=[GW_ASSET_TYPES[0], GW_ASSET_TYPES[1], GW_ASSET_TYPES[2], ILM_ASSET_TYPES[1]])
            asset_details = asset_service.asset_related_asset(query=asset_query)
            request_handler.update_service_response(
                asset_service.construct_asset_details(data=asset_details.get("data"), asset_type="Entities"))
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't get lightPoint, ccms and hub details %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def entities_search(request):
    request_handler = RequestResponseHandler()
    try:
        entity_name = request.GET.get("name")
        if request.GET.get('name'):
            device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
            device_query = device_service.construct_find_query(entity_type=ENTITY_TYPE[0],
                                                               text_search=entity_name.upper())
            asset_query = device_service.construct_find_query(entity_type=ENTITY_TYPE[1],
                                                              text_search=entity_name.upper())
            devices = device_service.find_entities_by_query(query=device_query)
            assets = device_service.find_entities_by_query(query=asset_query)
            response_data = device_service.construct_response_data(device_data=devices["data"],
                                                                   asset_data=assets["data"])
            request_handler.update_service_response(response_data)
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("couldn't search entities %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def entity_detail(request):
    request_handler = RequestResponseHandler()
    name = None
    try:
        _entity_name = request.GET.get("entityName")
        logger.info(f"Looking entity detail for {_entity_name}")
        if _entity_name:
            device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
            asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
            device = device_service.get_entity_by_name(entity_type=ENTITY_TYPE[0], name=_entity_name)
            if device.get("status") == 400 or device.get("status") == 404:
                device = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1], name=_entity_name)
            device_id = device.get("id").get("id")
            owner_id = device.get("ownerId").get("id")
            wards_all_details = []
            entity_view = []

            _entity_profile = device.get('type').lower()
            logger.info(f"Fetching entity detail for {_entity_name} from {_entity_profile}")
            if _entity_profile in (DEVICE_TYPES[1], DEVICE_TYPES[2]):
                # ILM or ILM-4g
                ilm_query = device_service.device_level_entity(entity_id=device_id, entity_type=ENTITY_TYPE[0])
                ilm_details = device_service.find_entities_by_query(query=ilm_query)
                ilm_info = device_service.construct_ilm_gw_details(data=ilm_details["data"])
                lp_query_const = device_service.device_relations(entity_id=device_id, entity_type=ENTITY_TYPE[0],
                                                                 direction=ENTITY_RELATIONS[1],
                                                                 relation_type=RELATION_TYPES[1])
                lp_entity_query = device_service.find_entities_by_query(query=lp_query_const)
                if lp_entity_query.get('data'):
                    lp_info = device_service.relation_construct(data=lp_entity_query["data"], attributes=True)
                    lamp_query_const = asset_service.construct_asset_relation_query(entity_id=lp_info.get('id'),
                                                                                    entity_type=ENTITY_TYPE[1],
                                                                                    direction=ENTITY_RELATIONS[0],
                                                                                    relation_type=RELATION_TYPES[2])
                    lamp_entity_query = device_service.find_entities_by_query(query=lamp_query_const)
                    lamp_info = asset_service.lamp_details_construct(data=lamp_entity_query["data"])
                    pole_query_const = asset_service.construct_asset_relation_query(entity_id=lp_info.get("id"),
                                                                                    entity_type=ENTITY_TYPE[1],
                                                                                    direction=ENTITY_RELATIONS[1],
                                                                                    relation_type=RELATION_TYPES[5])
                    pole_entity_query = device_service.find_entities_by_query(query=pole_query_const)
                    pole_info = {}
                    if pole_entity_query.get('data'):
                        pole_info = asset_service.pole_details_construct(data=pole_entity_query["data"])
                    wards_all_details = device_service.get_all_to_relations(data=lp_info)
                    entity_view.append(device_service.construct_return_device_response(device=ilm_info, lp=lp_info,
                                                                                       lamp=lamp_info, pole=pole_info))
                else:
                    entity_view.append(device_service.construct_return_device_response(device=ilm_info, lp={},
                                                                                       lamp={}, pole={}))
            elif _entity_profile in (DEVICE_TYPES[0], DEVICE_TYPES[4]):
                # gw, nic
                logger.debug(f"{_entity_profile} in {(DEVICE_TYPES[0], DEVICE_TYPES[4])}")
                gw_query = device_service.device_level_entity(entity_id=device_id, entity_type=ENTITY_TYPE[0])
                gw_details = device_service.find_entities_by_query(query=gw_query)
                gw_info = device_service.construct_ilm_gw_details(data=gw_details["data"])
                asset_query = device_service.gw_device_relations(entity_id=device_id, entity_type=ENTITY_TYPE[0],
                                                                 direction=ENTITY_RELATIONS[1],
                                                                 relation_type=RELATION_TYPES[1])
                asset_detail = device_service.find_entities_by_query(query=asset_query)
                if asset_detail.get('data'):
                    asset_info = asset_service.construct_ccms_details(data=asset_detail["data"])
                    if asset_info.get('type') in (GW_ASSET_TYPES[0], GW_ASSET_TYPES[2]):
                        entity_view.append(device_service.construct_return_device_response(
                            gw=gw_info, ccms=asset_info))
                    else:
                        asset_info = device_service.relation_construct(data=asset_detail["data"], attributes=True)
                        entity_view.append(device_service.construct_return_device_response(gw=gw_info, hub=asset_info))
                    wards_all_details = device_service.get_all_to_relations(data=asset_info)
                else:
                    entity_view.append(device_service.construct_return_device_response(gw=gw_info))
            elif _entity_profile == ILM_ASSET_TYPES[0]:
                logger.debug(f"{_entity_profile} == {ILM_ASSET_TYPES[0]}")
                lamp_query = asset_service.asset_details_query_construct(entity_id=device_id,
                                                                         entity_type=ENTITY_TYPE[1])
                lamp_details = device_service.find_entities_by_query(query=lamp_query)
                device_info = asset_service.lamp_details_construct(data=lamp_details["data"])
                lp_relation_query = device_service.device_relations(entity_id=device_id, entity_type=ENTITY_TYPE[1],
                                                                    direction=ENTITY_RELATIONS[1],
                                                                    relation_type=RELATION_TYPES[2])
                lp_detail = device_service.find_entities_by_query(query=lp_relation_query)
                if lp_detail.get('data'):
                    lp_info = device_service.relation_construct(data=lp_detail["data"],
                                                                attributes=True)
                    ilm_relation_query = device_service.device_relations(entity_id=lp_info.get('id'),
                                                                         entity_type=ENTITY_TYPE[1],
                                                                         direction=ENTITY_RELATIONS[0],
                                                                         relation_type=RELATION_TYPES[1])
                    ilm_detail = device_service.find_entities_by_query(query=ilm_relation_query)
                    ilm_info = device_service.construct_ilm_gw_details(data=ilm_detail["data"])
                    pole_query_const = asset_service.construct_asset_relation_query(entity_id=lp_info.get("id"),
                                                                                    entity_type=ENTITY_TYPE[1],
                                                                                    direction=ENTITY_RELATIONS[1],
                                                                                    relation_type=RELATION_TYPES[5])
                    pole_entity_query = device_service.find_entities_by_query(query=pole_query_const)
                    pole_info = {}
                    if pole_entity_query.get('data'):
                        pole_info = asset_service.pole_details_construct(data=pole_entity_query["data"])
                    wards_all_details = device_service.get_all_to_relations(data=lp_info)
                    entity_view.append(device_service.construct_return_device_response(device=ilm_info, lp=lp_info,
                                                                                       lamp=device_info,
                                                                                       pole=pole_info))
                else:
                    entity_view.append(device_service.construct_return_device_response(device={}, lp={},
                                                                                       lamp=device_info, pole={}))
            elif _entity_profile == ILM_ASSET_TYPES[1].lower():
                logger.info(f"{_entity_profile} == {ILM_ASSET_TYPES[1]}")
                lp_query = asset_service.asset_details_query_construct(entity_id=device_id,
                                                                       entity_type=ENTITY_TYPE[1])
                lp_details = device_service.find_entities_by_query(query=lp_query)
                lp_info = device_service.relation_construct(data=lp_details["data"], attributes=True)
                ilm_relation_query = device_service.device_relations(entity_id=device_id,
                                                                     entity_type=ENTITY_TYPE[1],
                                                                     direction=ENTITY_RELATIONS[0],
                                                                     relation_type=RELATION_TYPES[1])
                ilm_relation_detail = device_service.find_entities_by_query(query=ilm_relation_query)
                ilm_info = device_service.construct_ilm_gw_details(data=ilm_relation_detail["data"])
                lamp_relation_query = asset_service.construct_asset_relation_query(entity_id=device_id,
                                                                                   entity_type=ENTITY_TYPE[1],
                                                                                   direction=ENTITY_RELATIONS[0],
                                                                                   relation_type=RELATION_TYPES[2])
                lamp_relation_detail = device_service.find_entities_by_query(query=lamp_relation_query)
                lamp_info = asset_service.lamp_details_construct(data=lamp_relation_detail["data"])
                wards_all_details = device_service.get_all_to_relations(data=lp_info)
                pole_query_const = asset_service.construct_asset_relation_query(entity_id=device_id,
                                                                                entity_type=ENTITY_TYPE[1],
                                                                                direction=ENTITY_RELATIONS[1],
                                                                                relation_type=RELATION_TYPES[5])
                pole_entity_query = device_service.find_entities_by_query(query=pole_query_const)
                pole_info = {}
                if pole_entity_query.get('data'):
                    pole_info = asset_service.pole_details_construct(data=pole_entity_query["data"])
                entity_view.append(device_service.construct_return_device_response(device=ilm_info, lp=lp_info,
                                                                                   lamp=lamp_info, pole=pole_info))
            elif _entity_profile in (GW_ASSET_TYPES[0], GW_ASSET_TYPES[2]):
                # ccms/ smslc
                logger.debug(f"{_entity_profile} == {(GW_ASSET_TYPES[0], GW_ASSET_TYPES[2])}")
                ccms_query = asset_service.asset_details_query_construct(entity_id=device_id,
                                                                         entity_type=ENTITY_TYPE[1])
                ccms_details = device_service.find_entities_by_query(query=ccms_query)
                ccms_info = asset_service.construct_ccms_details(data=ccms_details["data"])
                gw_query = device_service.gw_device_relations(entity_id=device_id, entity_type=ENTITY_TYPE[1],
                                                              direction=ENTITY_RELATIONS[0],
                                                              relation_type=RELATION_TYPES[1])
                gw_detail = device_service.find_entities_by_query(query=gw_query)
                gw_info = device_service.construct_ilm_gw_details(data=gw_detail["data"])
                wards_all_details = device_service.get_all_to_relations(data=ccms_info)
                entity_view.append(device_service.construct_return_device_response(gw=gw_info, ccms=ccms_info))
            elif _entity_profile == GW_ASSET_TYPES[1]:
                # hub
                logger.debug(f"{_entity_profile} == {GW_ASSET_TYPES[1]}")
                hub_query = asset_service.asset_details_query_construct(entity_id=device_id, entity_type=ENTITY_TYPE[1])
                hub_details = device_service.find_entities_by_query(query=hub_query)
                hub_info = device_service.relation_construct(data=hub_details["data"], attributes=True)
                gw_query = device_service.gw_device_relations(entity_id=device_id, entity_type=ENTITY_TYPE[1],
                                                              direction=ENTITY_RELATIONS[0],
                                                              relation_type=RELATION_TYPES[1])
                gw_detail = device_service.find_entities_by_query(query=gw_query)
                gw_info = device_service.construct_ilm_gw_details(data=gw_detail["data"])
                wards_all_details = device_service.get_all_to_relations(data=hub_info)
                entity_view.append(device_service.construct_return_device_response(gw=gw_info, hub=hub_info))
            elif _entity_profile == ILM_ASSET_TYPES[5].lower():
                logger.debug(f"{_entity_profile} == {ILM_ASSET_TYPES[5]}")
                pole_query = asset_service.asset_details_query_construct(entity_id=device_id,
                                                                         entity_type=ENTITY_TYPE[1])
                pole_details = device_service.find_entities_by_query(query=pole_query)
                pole_detail = asset_service.pole_details_construct(data=pole_details["data"])
                wards_all_details = device_service.get_all_to_relations(data=pole_detail)
                pole_relation_query = device_service.get_from_relations_info(from_id=pole_detail.get("id"),
                                                                             from_type=ENTITY_TYPE[1])
                lamp_relation_construct = asset_service.construct_ward_response(data=pole_relation_query)
                pole_detail.update({"relations": lamp_relation_construct})
                entity_view.append(device_service.construct_return_device_response(pole=pole_detail))
            else:
                logger.debug(f"{_entity_profile} == No Match")
            request_handler.update_service_response({"customer": {"id": owner_id}, "wardDetails": wards_all_details,
                                                     "deviceDetails": entity_view})
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't get device details %s ... %s" % (name, str(e)))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def ilm_control(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        action = request_params.get(ILM_TEST_QUERY_PARAMS[2])
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        device_id = request_params.get(ILM_TEST_QUERY_PARAMS[1])
        if action.lower() in device_service.ilm_rpc:
            device_service.device_control(device_id=device_id, data=json.dumps(device_service.ilm_rpc[action.lower()]))
        else:
            request_handler.update_service_response({"status": 1000, "message": "Please check the action type"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("ilm device couldn't pass rpc call %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def gw_install(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        ward_id = request_params.get('wardId')
        customer_id = request_params.get("customerId")
        gw_device_id = request_params.get("deviceId")
        img_data = request_params.get('auditImg')
        region = request_params.get('region')
        telemetry_data = {"ts": request_params.get("installedOn"),
                          "values": {"auditImg": img_data, "pkt": 102, "activity": "Install"}}
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        relation_details = device_service.device_relations(entity_id=gw_device_id,
                                                           entity_type=ENTITY_TYPE[0], direction=ENTITY_RELATIONS[1],
                                                           relation_type=RELATION_TYPES[1])
        to_relation_details = device_service.find_entities_by_query(query=relation_details)
        panel_id = ""
        if len(to_relation_details.get("data")) > 0:
            for ccms_asset in to_relation_details.get("data"):
                if panel_id == "":
                    _entity_profile = ccms_asset.get("latest").get("ENTITY_FIELD").get("type").get("value")
                    if _entity_profile in (GW_ASSET_TYPES[0], GW_ASSET_TYPES[1]):
                        panel_id = ccms_asset.get("entityId").get("id")
                    else:
                        panel_id = ccms_asset.get("entityId").get("id")
                else:
                    request_handler.update_service_response(
                        {"status": 1000, "message": "GW Dispatched With Multiple CCMS Asset"})
        else:
            request_handler.update_service_response({"status": 1000, "message": "GW Not Dispatched Yet"})
        device_service.change_owner(owner_type=OWNER_TYPE[0], owner_id=customer_id,
                                    entity_id=gw_device_id)
        asset_service.change_owner(owner_type=OWNER_TYPE[0], owner_id=customer_id,
                                   entity_id=panel_id)
        asset_service.create_entity_relation(from_id=ward_id, from_type=ENTITY_TYPE[1], to_id=panel_id,
                                             to_type=ENTITY_TYPE[1], relation_type=RELATION_TYPES[0])
        ccms_attributes = device_service.construct_attributes(
            request_attributes=request_params,
            valid_keys=CCMS_INSTALLATION_ATTRIBUTES + GW_INSTALLATION_ATTRIBUTES[0:2])
        gw_attributes = device_service.construct_attributes(request_attributes=request_params,
                                                            valid_keys=GW_INSTALLATION_ATTRIBUTES)
        device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                               entity_id=panel_id, scope=ATTRIBUTE_SCOPES[1],
                                               data=json.dumps(ccms_attributes))
        device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0],
                                               entity_id=gw_device_id, scope=ATTRIBUTE_SCOPES[1],
                                               data=json.dumps(gw_attributes))
        asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=panel_id, data=telemetry_data)
        asset_service.update_telemetry(entity_type=ENTITY_TYPE[0], entity_id=gw_device_id, data=telemetry_data)

        # Placing SLC/CCMS under region group
        asset_service.place_entity_under_group(
            entity_ids=[panel_id], owner_type=OWNER_TYPE_CUSTOMER,
            owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=region)

        # Placing GW under region group
        device_service.place_entity_under_group(
            entity_ids=[gw_device_id], owner_type=OWNER_TYPE_CUSTOMER,
            owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=region)

    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("gw device couldn't install %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def gw_control(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        action = request_params.get(ILM_TEST_QUERY_PARAMS[2])
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        device_id = request_params.get(ILM_TEST_QUERY_PARAMS[1])
        if action.lower() in device_service.gw_rpc:
            device_service.device_control(device_id=device_id, data=json.dumps(device_service.gw_rpc[action.lower()]))
        else:
            request_handler.update_service_response({"status": 1000, "message": "Please check the action type"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("gw device couldn't pass rpc call request %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def gw_replace(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)

        if request_params.get("replaceWith"):
            gw_device_id = request_params.get("deviceId")
            ccms_asset_id = request_params.get("ccmsId")
            get_replace_device_details = request_params.get("replaceWith")
            img_data = get_replace_device_details.get('auditImg')
            region = get_replace_device_details.get("region")
            telemetry_data = {"ts": get_replace_device_details.get("installedOn"),
                              "values": {"auditImg": img_data, "pkt": 102, "activity": "Remove"}}
            asset_details = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1], entity_id=ccms_asset_id)
            old_relations = device_service.get_and_remove_from_and_to_relations(entity_id=gw_device_id,
                                                                                entity_type=ENTITY_TYPE[0])
            gw_attributes = device_service.construct_attributes(
                request_attributes=REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION,
                valid_keys=GW_INSTALLATION_ATTRIBUTES)
            device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0], entity_id=gw_device_id,
                                                   scope=ATTRIBUTE_SCOPES[1], data=json.dumps(gw_attributes))
            asset_service.update_telemetry(entity_type=ENTITY_TYPE[0], entity_id=gw_device_id, data=telemetry_data)
            if "name" in get_replace_device_details:
                new_device_name = get_replace_device_details.get("name")
                new_device = device_service.get_entity_by_name(entity_type=ENTITY_TYPE[0], name=new_device_name)
                new_device_id = new_device.get("id").get("id")
                customer_id = get_replace_device_details.get("customerId")
                device_service.get_and_remove_from_and_to_relations(entity_id=new_device_id, entity_type=ENTITY_TYPE[0])
                # remove old gw -(same region , owner id  to remove old gw and add new gw)
                try:
                    gw_info = device_service.get_entity_info_by_id(entity_type=ENTITY_TYPE_DEVICE,
                                                                   entity_id=gw_device_id)
                    for group in gw_info.get("groups", []):
                        rem_region = group.get("name")
                        rem_customer_id = gw_info.get("ownerId", {}).get("id")
                        if rem_customer_id != customer_id:
                            device_service.remove_entity_under_group(
                                entity_ids=[gw_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                                owner_id=rem_customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=rem_region)
                        else:
                            if rem_region != region:
                                device_service.remove_entity_under_group(
                                    entity_ids=[gw_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                                    owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=rem_region)
                            else:
                                device_service.remove_entity_under_group(
                                    entity_ids=[gw_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                                    owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=region)
                except Exception as e:
                    logger.exception("remove gw under regional group in replacement activity", e)

                device_service.create_from_and_to_relations(from_relation=old_relations[0],
                                                            to_relation=old_relations[1],
                                                            entity_id=new_device_id, entity_type=ENTITY_TYPE[0])
                asset_details["label"] = new_device_name
                asset_service.create_asset(entity_type=ENTITY_TYPE[1], data=asset_details)
                new_device["label"] = asset_details.get("name")
                device_service.create_device(entity_type=ENTITY_TYPE[0], data=new_device)
                ccms_server_attributes = asset_service.get_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                                            entity_id=ccms_asset_id,
                                                                            scope=ATTRIBUTE_SCOPES[1],
                                                                            keys=LP_SERVER_ATTRIBUTES[0])
                for attribute in ccms_server_attributes:
                    if float(attribute["value"]) > float(get_replace_device_details.get("accuracy")):
                        ccms_attributes = device_service.construct_attributes(
                            request_attributes=get_replace_device_details,
                            valid_keys=CCMS_INSTALLATION_ATTRIBUTES[0:7])
                        device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                                               entity_id=ccms_asset_id, scope=ATTRIBUTE_SCOPES[1],
                                                               data=json.dumps(ccms_attributes))
                gw_attributes = device_service.construct_attributes(request_attributes=get_replace_device_details,
                                                                    valid_keys=GW_INSTALLATION_ATTRIBUTES)
                device_service.update_entity_attribute(entity_type=ENTITY_TYPE[0],
                                                       entity_id=new_device_id, scope=ATTRIBUTE_SCOPES[1],
                                                       data=json.dumps(gw_attributes))
                telemetry_data = {"ts": get_replace_device_details.get("installedOn"),
                                  "values": {"auditImg": img_data, "pkt": 102, "activity": "Install"}}
                asset_service.update_telemetry(entity_type=ENTITY_TYPE[0], entity_id=new_device_id, data=telemetry_data)
                asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=ccms_asset_id, data=telemetry_data)
                if customer_id != new_device.get("ownerId").get("id"):
                    device_service.change_owner(owner_type=OWNER_TYPE[0], owner_id=customer_id, entity_id=new_device_id)
                # add new  gw -(same region , owner id  to remove old gw and add new gw)
                try:
                    # Placing GW under region group
                    device_service.place_entity_under_group(
                        entity_ids=[new_device_id], owner_type=OWNER_TYPE_CUSTOMER,
                        owner_id=customer_id, group_type=ENTITY_TYPE_DEVICE, group_name=region)
                except Exception as e:
                    logger.exception("placing gw under regional group", e)
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("gw device couldn't replace %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def ebmeter_replace(request):
    request_handler = RequestResponseHandler()
    ebmeter_service = EbMeterService()
    try:
        request_params = json.loads(request.body)
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        ebmeter_service.replace_ebmeter(
            request_handler=request_handler, asset_service=asset_service, request_params=request_params)
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("eb meter not replaced  %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def ebmeter_isinstallable(request):
    request_handler = RequestResponseHandler()
    try:
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        _eb_meter_no = request.GET.get("ebMeterNo")
        query = {
            "entityFilter": {
                "type": "assetType",
                "assetType": "ccms"

            },
            "keyFilters": [
                {
                    "key": {
                        "type": "SERVER_ATTRIBUTE",
                        "key": "ebMeterNo"
                    },
                    "valueType": "STRING",
                    "predicate": {
                        "operation": "EQUAL",
                        "value": {
                            "defaultValue": _eb_meter_no,
                            "dynamicValue": None
                        },
                        "type": "STRING"
                    }
                }
            ],
            "pageLink": {
                "page": 0,
                "pageSize": 10,
                "sortOrder": {
                    "key": {
                        "key": "name",
                        "type": "ENTITY_FIELD"
                    },
                    "direction": "ASC"
                }
            }
        }
        _data = asset_service.find_entities_by_query(query=query)
        _state = "installable"
        if len(_data.get("data")) > 0:
            _state = "installed"
        return HttpResponse(json.dumps({"state": _state}))
    except Exception as e:
        request_handler.update_error_response("couldn't check ebmeter details %s" % str(e))
        logger.exception("couldn't check ebmeter details %s" % str(e))
        return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def image_upload_to_aws(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        device_name = request_params.get("name")
        util_service = UtilService()
        if "auditImg" in request_params:
            if request_params.get("activity") == "grievanceAppIssues":
                util_service.upload_image_to_aws(
                    request_handler=request_handler,
                    img_data=request_params.get("auditImg"),
                    file_name=device_name,
                    folder=AWS_BUCKET_GRIEVANCE_FOLDER_NAME
                )
            else:
                util_service.upload_image_to_aws(
                    request_handler=request_handler,
                    img_data=request_params.get("auditImg"),
                    file_name=device_name,
                    folder=AWS_BUCKET_LUMINATOR_FOLDER_NAME
                )
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("image couldn't upload to aws s3 bucket %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_asset_lat_lon(request):
    request_handler = RequestResponseHandler()
    try:
        ward_id = request.GET.get('wardId')
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        asset_query = asset_service.asset_lat_lon(
            entity_id=str(ward_id), entity_type=ENTITY_TYPE[1], relation_type=RELATION_TYPES[0],
            asset_type=[GW_ASSET_TYPES[0], GW_ASSET_TYPES[1], GW_ASSET_TYPES[2], ILM_ASSET_TYPES[1]])
        asset_details = asset_service.asset_related_asset(query=asset_query)
        request_handler.update_service_response(asset_service.construct_asset_lat_lon_details(
            data=asset_details.get("data")))
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("couldn't get lightPoint, ccms and hub details with lat,lon %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@validate_keys(['latitude', 'longitude', 'location'])
@method_validate("POST")
def pole_install(request):
    request_handler = RequestResponseHandler()
    try:
        request_handler = RequestResponseHandler()
        request_params = json.loads(request.body)
        img_data = request_params.get('auditImg')
        telemetry_data = {"ts": request_params.get("installedOn"),
                          "values": {"auditImg": img_data, "pkt": 102, "activity": "Install"}}
        ward_id = request_params.get('wardId')
        customer_id = request_params.get("customerId")
        region = request_params.get('region')
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        customer_service = CustomerService(token=request.headers.get('token'), instance=request_handler)
        customer_details = customer_service.get_customer_by_id(owner_type=OWNER_TYPE[0],
                                                               owner_id=request_params.get("customerId"))
        construct_new_pole_asset = asset_service.create_pole_asset_name(data=request_params)
        create_pole = asset_service.create_and_update_asset_name(details=construct_new_pole_asset)
        pole_id = create_pole.get("id")
        pole_attributes = device_service.construct_attributes(request_attributes=request_params,
                                                              valid_keys=POLE_INSTALLATION_ATTRIBUTES)
        device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                               entity_id=pole_id, scope=ATTRIBUTE_SCOPES[1],
                                               data=json.dumps(pole_attributes))
        request_handler.update_service_response(asset_service.create_entity_relation(from_id=ward_id,
                                                                                     from_type=ENTITY_TYPE[1],
                                                                                     to_id=pole_id,
                                                                                     to_type=ENTITY_TYPE[1],
                                                                                     relation_type=RELATION_TYPES[0]))
        asset_service.publish_pole_data_to_pubsub(pole_data=request_params, customer_details=customer_details)
        asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=pole_id, data=telemetry_data)

        # Placing Lamp under region group
        device_service.place_entity_under_group(
            entity_ids=[pole_id], owner_type=OWNER_TYPE_CUSTOMER,
            owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=region)

    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("couldn't install pole asset %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_pole(request):
    request_handler = RequestResponseHandler()
    pole_name = None
    try:
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        pole_name = request.GET.get("poleNo")
        pole_details = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1], name=pole_name)
        pole_id = pole_details.get("id").get("id")
        pole_query = asset_service.asset_details_query_construct(entity_id=pole_id, entity_type=ENTITY_TYPE[1])
        pole_asset_detail = device_service.find_entities_by_query(query=pole_query)
        request_handler.update_service_response(
            asset_service.pole_details_construct(data=pole_asset_detail['data'], pole_name=pole_name))
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't get pole %s ... %s" % (pole_name, str(e)))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_pole_count(request):
    request_handler = RequestResponseHandler()
    try:
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        ward_id = request.GET.get("wardId")
        asset_type = request.GET.get("assetType")
        if request.GET.get("wardId") and request.GET.get("assetType"):
            if asset_type == ILM_ASSET_TYPES[5]:
                asset_query = asset_service.asset_level_entity(entity_id=str(ward_id),
                                                               asset_type=[asset_type],
                                                               relation_type=RELATION_TYPES[0], max_level=1,
                                                               entity_type=ENTITY_TYPE[1])
                asset_details = asset_service.asset_related_asset(query=asset_query)
                request_handler.update_service_response({"pole_count": len(asset_details["data"])})
            elif asset_type == ILM_ASSET_TYPES[0]:
                asset_query = asset_service.asset_level_entity(entity_id=str(ward_id),
                                                               asset_type=[asset_type],
                                                               relation_type=RELATION_TYPES[2], max_level=2,
                                                               entity_type=ENTITY_TYPE[1])
                asset_details = asset_service.asset_related_asset(query=asset_query)
                request_handler.update_service_response({"lamp_count": len(asset_details["data"])})
            else:
                request_handler.update_service_response({"status": 1000, "message": "Invalid asset type"})
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("couldn't get asset count %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_asset_installation_history(request):
    request_handler = RequestResponseHandler()
    try:
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        if request.GET.get("wardId") and request.GET.get("assetType") and request.GET.get("user"):
            if "region_id" in request.GET:
                asset_id = request.GET.get("regionId")
                asset_type = request.GET.get("assetType")
            elif "zone_id" in request.GET:
                asset_id = request.GET.get("zoneId")
                asset_type = request.GET.get("assetType")
            else:
                asset_id = request.GET.get("wardId")
                asset_type = request.GET.get("assetType")
            user_name = request.GET.get("user")
            if asset_type == ILM_ASSET_TYPES[5]:
                asset_query = asset_service.construct_asset_installation_history(user=user_name, asset_id=asset_id,
                                                                                 asset_type=asset_type,
                                                                                 relation_type=RELATION_TYPES[0])
                asset_details = asset_service.asset_related_asset(query=asset_query)
                request_handler.update_service_response(asset_service.construct_date_wise_asset_installation_count(
                    details=asset_details['data']))
            elif asset_type == ILM_ASSET_TYPES[0]:
                asset_query = asset_service.construct_asset_installation_history(user=user_name, asset_id=asset_id,
                                                                                 asset_type=asset_type,
                                                                                 relation_type=RELATION_TYPES[2])
                asset_details = asset_service.asset_related_asset(query=asset_query)
                request_handler.update_service_response(asset_service.construct_date_wise_asset_installation_count(
                    details=asset_details['data']))
            else:
                request_handler.update_service_response({"status": 400, "message": "Invalid Asset Type"})
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("couldn't get installation history for user %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_project_wise_wattage(request):
    request_handler = RequestResponseHandler()
    try:
        if request.GET.get("customerId") and request.GET.get("lampProfiles"):
            asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
            customer_service = CustomerService(token=request.headers.get('token'), instance=request_handler)
            customer_id = request.GET.get("customerId")
            lamp_profile = json.loads(request.GET.get("lampProfiles"))
            get_customer_attribute = asset_service.get_entity_attribute(entity_id=customer_id,
                                                                        entity_type=OWNER_TYPE[0],
                                                                        scope=ATTRIBUTE_SCOPES[1],
                                                                        keys=PROJECT_WISE_WATTAGE)
            request_handler.update_service_response(customer_service.wattage_construct(data=get_customer_attribute,
                                                                                       lamp_profiles=lamp_profile))
        else:
            request_handler.update_service_response({"status": 400, "message": "Invalid request body"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("couldn't get project wise wattage %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
@request_parameter_validate()
def luminaire_maintain(request):
    request_handler = RequestResponseHandler()
    try:
        request_params = json.loads(request.body)
        logger.info('request_params : %s', request_params)
        _token = request.headers.get('token')
        _pole_id = request_params.get("poleId")
        tenent_token = dashboard_login(BASE_URL, LOGIN_CREDENTIALS)

        asset_service = AssetService(token=_token, instance=request_handler, tenent_token=tenent_token)
        device_service = DeviceService(token=_token, instance=request_handler)
        lamp_service = LampService(token=_token, instance=request_handler)
        customer_service = CustomerService(token=_token, instance=request_handler)

        if request_params.get("remove"):
            if request_params.get("remove").get("lampId") and request_params.get("remove").get("ilmId"):
                device_service.replace_ilm_and_lamp(request_params, device_service, asset_service, request_handler,
                                                    lamp_service, customer_service)
            elif request_params.get("remove").get("ilmId"):
                device_service.ilm_replace(request_params, asset_service, device_service, request_handler)
            elif request_params.get("remove").get("lampId"):
                lamp_service.replace_lamp(request_params, asset_service, device_service, lamp_service, request_handler,
                                          customer_service)
            else:
                request_handler.update_error_response("Unexpected Request")
        else:
            _installation_type = None
            if request_params.get("ilm"):
                _installation_type = "ilm"
            elif request_params.get("lamp"):
                _installation_type = "lamp"

            _can_install = True
            if _pole_id:
                _can_install = asset_service.is_pole_arms_free_to_install(
                    pole_id=_pole_id, installation_type=_installation_type, lp_id=request_params.get("lightPointId"))
            request_handler.update_service_response({"status": 200})
            if _can_install and request_params.get("ilm"):
                device_service.install_ilm(request_params, asset_service, device_service, request_handler,
                                           customer_service)
            elif _can_install and request_params.get("lamp"):
                lamp_service.install_lamp(request_params, asset_service, device_service, request_handler,
                                          customer_service)
            else:
                request_handler.update_error_response("Request ignored: Lightpoint count exceeds pole's arm count")
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't maintain luminaire - %s" % str(e))
    response = request_handler.get_service_response()
    return HttpResponse(response)


def ticket_login(base_url=None, credentials=None):
    api_url = base_url + "/api/auth/login"
    auth_header = {'Content-type': 'application/json', 'Accept': '*/*'}
    try:
        login_response = session.post(api_url, headers=auth_header, data=credentials, timeout=30)
        login_response.raise_for_status()
        auth_token = json.loads(login_response.text).get('token')
        auth_header['X-Authorization'] = 'Bearer ' + auth_token
    except Exception as e:
        logger.exception("Ticket Login Failed - %s" % str(e))
    return auth_header


def get_customer_details(customer_id, header):
    try:
        customer_url = BASE_URL + "/api/plugins/telemetry/CUSTOMER/" + \
                       customer_id + "/values/attributes?keys=ticketDetail"
        query_response = session.get(customer_url, headers=header, data='', timeout=90, verify=False)
        if query_response.status_code == 401:
            header = ticket_login(BASE_URL, LOGIN_CREDENTIALS)
            return get_customer_details(customer_id, header)
        elif query_response.status_code == 200:
            ticket_details = json.loads(query_response.text)
            ticket_count = int(ticket_details[0]["value"]["count"]) if ticket_details[0] else ""
            customer = ticket_details[0]["value"]["customer"] if ticket_details[0] else ""
            return customer, ticket_count
        else:
            logger.info("Cannot get customer detail for %s , due to response status code - %s" % (
                customer_id, query_response.status_code))
    except Exception as e:
        logger.exception("Cannot get customer detail for id %s due to error - %s" % (customer_id, str(e)))
    return None


def post_ticket_count(customer_id, data, header):
    try:
        customer_post_api_url = BASE_URL + "/api/plugins/telemetry/CUSTOMER/" + customer_id + "/attributes/SERVER_SCOPE"
        update_customer_query = session.post(customer_post_api_url,
                                             headers=header, data=data, timeout=90, verify=False)
        if update_customer_query.status_code == 401:
            header = ticket_login(BASE_URL, LOGIN_CREDENTIALS)
            return post_ticket_count(customer_id, data, header)
        elif update_customer_query.status_code == 200:
            return update_customer_query.status_code
    except Exception as e:
        logger.exception("Ticket Count detail update failed for customer %s due to  - %s" % (customer_id, str(e)))
    return None


def post_ticket_name(alarm_id, ticket_name, header):
    try:
        ticket_api_url = BASE_URL + "/api/alarm/" + alarm_id
        query_response = session.get(ticket_api_url, headers=header, data='', timeout=90, verify=False)
        if query_response.status_code == 401:
            header = ticket_login(BASE_URL, LOGIN_CREDENTIALS)
            return post_ticket_name(alarm_id, ticket_name, header)
        elif query_response.status_code == 200:
            ticket_details = json.loads(query_response.text)
            ticket_details["details"]["ticket_name"] = ticket_name
            data = json.dumps(ticket_details)
            ticket_name_post_api_url = BASE_URL + "/api/alarm"
            ticket_post_response = session.post(ticket_name_post_api_url, headers=header, data=data, timeout=90,
                                                verify=False)
            return json.loads(ticket_post_response.text)
    except Exception as e:
        logger.exception("Ticket name update failed for alarm id %s due to  - %s" % (alarm_id, str(e)))
    return None


@method_validate("GET")
def ticket_name_serializer(request):
    request_handler = RequestResponseHandler()
    alarm_id = request.GET.get("alarm_id")
    customer_id = request.GET.get("customer_id")
    try:
        header = ticket_login(BASE_URL, LOGIN_CREDENTIALS)
        customer, ticket_count = get_customer_details(customer_id, header)
        new_ticket_count = int(ticket_count) + 1
        data = json.dumps({"ticketDetail": {"customer": customer, "count": new_ticket_count}})
        update_query = post_ticket_count(customer_id, data, header)
        ticket_name = f"{customer}{new_ticket_count:06d}"
        post_ticket = post_ticket_name(alarm_id, ticket_name, header)
        request_handler.update_service_response(post_ticket)
    except Exception as e:
        logger.warn("Cannot create a ticket name %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


def dashboard_login(base_url=None, credentials=None):
    api_url = base_url + "/api/auth/login"
    auth_header = {'Content-type': 'application/json', 'Accept': '*/*'}
    auth_token = ""
    try:
        login_response = session.post(api_url, headers=auth_header, data=credentials, timeout=30)
        login_response.raise_for_status()
        auth_token = json.loads(login_response.text).get('token')
        auth_header['X-Authorization'] = 'Bearer ' + auth_token
    except Exception as e:
        logger.exception("Dashboard Login Failed - %s" % str(e))
    return auth_token


def update_mapped_lamp_detail(ccms_id, asset_service):
    try:
        asset_service = asset_service
        lp_query = asset_service.asset_level_entity(
            entity_type=ENTITY_TYPE[1], entity_id=ccms_id, relation_type=RELATION_TYPES[3],
            asset_type=[ILM_ASSET_TYPES[1]], max_level=1)
        pole_relation_data = asset_service.find_entities_by_query(query=lp_query)
        ccms_json = []
        if pole_relation_data.get("data"):
            for detail in pole_relation_data["data"]:
                lamp_watt = detail.get("latest", {}).get("SERVER_ATTRIBUTE", {}).get("lampWatts", {}).get("value")
                if lamp_watt:
                    existing_entry = next((item for item in ccms_json if item["watts"] == int(lamp_watt)), None)
                    if existing_entry:
                        existing_entry["count"] += 1
                    else:
                        watt_dict = {"watts": int(lamp_watt), "count": 1}
                        ccms_json.append(watt_dict)
                    tel_value = {"ts": int(time.time()) * 1000,
                                 "values": {"mappedLampDetail": ccms_json}}
                    asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=ccms_id, data=tel_value)
    except Exception as e:
        print(str(e))


@method_validate("GET")
def commission_lightpoint(request):
    request_handler = RequestResponseHandler()
    try:
        commissionservice = CommissionService()
        lp_id = request.GET.get("lpId")
        ccms_id = request.GET.get("ccmsId")
        ccms_name = request.GET.get("ccmsName")
        user_name = request.GET.get("userName")
        old_ccms_name = request.GET.get("oldccmsName")
        old_ccms_id = request.GET.get("oldccmsId")
        header = dashboard_login(BASE_URL, LOGIN_CREDENTIALS)
        asset_service = AssetService(token=str(header), instance=request_handler)
        attribute_data = {"poweredBy": ccms_name}
        pole_data = asset_service.get_relation_info_toid(
            to_id=lp_id, to_type=ENTITY_TYPE[1], relation_type=RELATION_TYPES[5])
        if pole_data:
            pole_id = pole_data[0]['from']['id']
            pole_relation = asset_service.get_relation_info_fromid(from_id=pole_id, from_type=ENTITY_TYPE[1],
                                                                   relation_type=RELATION_TYPES[5])
            if pole_relation:
                asset_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=pole_id,
                                                      scope=ATTRIBUTE_SCOPES[1], data=json.dumps(attribute_data))
                attribute_data.update({"state": "COMMISSIONED", "commissionedBy": user_name})
                for data in pole_relation:
                    lp_id = data['to']['id']
                    asset_service.update_entity_attribute(entity_type=ENTITY_TYPE[1], entity_id=lp_id,
                                                          scope=ATTRIBUTE_SCOPES[1], data=json.dumps(attribute_data))
            else:
                logger.info("Cannot get 'mount' relation data for pole id - %s" % pole_id)
        commissionservice.update_mapped_lamb_details(ccms_name=ccms_name, ccms_id=ccms_id, asset_service=asset_service)
        if old_ccms_id and old_ccms_name:
            commissionservice.update_mapped_lamb_details(ccms_name=old_ccms_name, ccms_id=old_ccms_id,
                                                         asset_service=asset_service)
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("couldn't add  relation attribute to lp and pole - %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


def update_latitude_longitude(request):
    request_handler = RequestResponseHandler()
    try:
        header = dashboard_login(BASE_URL, LOGIN_CREDENTIALS)
        asset_service = AssetService(token=str(header), instance=request_handler)
        latitude = request.GET.get("latitude")
        longitude = request.GET.get("longitude")
        pole_id = request.GET.get("entityId")
        if not latitude or not longitude or not pole_id:
            logging.info(
                "Missing required parameters: Latitude, Longitude, or lpId. Received - Latitude: %s, Longitude: %s, lpId: %s",
                latitude, longitude, pole_id)

        attribute_data = {"latitude": latitude, "longitude": longitude}

        pole_relation = asset_service.get_relation_info_fromid(from_id=pole_id, from_type=ENTITY_TYPE[1],
                                                               relation_type=RELATION_TYPES[5])
        lp_ids = [
            entry["to"]["id"] for entry in pole_relation if "to" in entry and "id" in entry["to"]
        ]
        for lp_id in lp_ids:
            asset_service.update_entity_attribute(
                entity_type=ENTITY_TYPE[1],
                entity_id=lp_id,
                scope=ATTRIBUTE_SCOPES[1],
                data=json.dumps(attribute_data),
            )
        pole_data = asset_service.get_entity_by_id(entity_type=ENTITY_TYPE[1], entity_id=pole_id)
        pole_name = pole_data.get("name")
        location_message = {
            "pole_id": pole_name,
            "latitude": latitude,
            "longitude": longitude
        }
        asset_service.publish_location_data_to_pubsub(message=location_message)
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't update latitude and longitude for lightpoint- %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("POST")
def upload_ppe_image(request):
    request_handler = RequestResponseHandler()
    try:
        _token = request.headers.get("token")
        ppe_image = request.POST.get('ppeImg')
        _img_extension = request.POST.get('imgExtension')
        _ts = request.POST.get('ts')
        datetime_from_ts = datetime.fromtimestamp(int(_ts) / 1000)
        current_date = datetime_from_ts.strftime("%Y-%m-%d")
        auth_service = TbAuthService(instance=request_handler)
        user_info = auth_service.get_user_details(header=request.headers.get("token"))
        user_id = user_info.get("id").get("id")
        file_name = f"{user_id}_{current_date}.{_img_extension.lower()}"
        util_service = UtilService()
        upload_response = util_service.upload_image_to_aws(
            request_handler=request_handler,
            img_data=ppe_image,
            file_name=file_name,
            folder=AWS_BUCKET_PPE_IMAGE_FOLDER_NAME
        )
        _username = user_info.get("name")
        asset_service = AssetService(token=_token, instance=request_handler)
        asset_service.update_telemetry(
            entity_type="USER",
            entity_id=user_id,
            data={"ppeImageUrl": upload_response.get("ppe_image_url")})
        request_handler.update_service_response(upload_response)
        logger.info("GreytHR: Check-In successful for the user %s" % str(_username))

    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("S3: Failed to upload image in aws s3 bucket %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@method_validate("POST")
def greyt_hr_swipes(request):
    request_handler = RequestResponseHandler()
    try:
        ts = request.POST.get('ts')
        employee_id = request.POST.get('employee_id')
        status = request.POST.get('status')
        attendance = AttendanceService()
        swipes = attendance.greythr_swipes(ts, employee_id, status)
        print(swipes)
        request_handler.update_service_response({"status": 200, "message": "success"})
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Failed to login %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@validate_keys(['latitude', 'longitude', 'location'])
@method_validate("POST")
def switch_point_install(request):
    request_handler = RequestResponseHandler()
    try:
        request_handler = RequestResponseHandler()
        request_params = json.loads(request.body)
        img_data = request_params.get('auditImg')
        telemetry_data = {"ts": request_params.get("installedOn"),
                          "values": {"auditImg": img_data, "pkt": 102, "activity": "Install"}}
        ward_id = request_params.get('wardId')
        customer_id = request_params.get("customerId")
        region = request_params.get('region')
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        customer_service = CustomerService(token=request.headers.get('token'), instance=request_handler)
        # customer_details = customer_service.get_customer_by_id(owner_type=OWNER_TYPE[0],
        #                                                        owner_id=request_params.get("customerId"))
        construct_new_switch_point_asset = asset_service.create_switch_point_asset_name(data=request_params)
        validate_asset = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1], name=construct_new_switch_point_asset.get('name'))

        # duplicate check
        if validate_asset.get("status") != 404:
            return HttpResponse(json.dumps({"status": 200}))

        create_switch_point = asset_service.create_and_update_asset_name(details=construct_new_switch_point_asset)
        switch_point_id = create_switch_point.get("id")
        switch_point_attributes = device_service.construct_attributes(request_attributes=request_params,
                                                              valid_keys=SWITCH_POINT_INSTALLATION_ATTRIBUTES)
        switch_point_attributes.pop("wardId")
        switch_point_attributes.pop("customerId")
        switch_point_attributes.pop("assetType")
        device_service.update_entity_attribute(entity_type=ENTITY_TYPE[1],
                                               entity_id=switch_point_id, scope=ATTRIBUTE_SCOPES[1],
                                               data=json.dumps(switch_point_attributes))
        
        request_handler.update_service_response(asset_service.create_entity_relation(from_id=ward_id,
                                                                                     from_type=ENTITY_TYPE[1],
                                                                                     to_id=switch_point_id,
                                                                                     to_type=ENTITY_TYPE[1],
                                                                                     relation_type=RELATION_TYPES[0]))
        # asset_service.publish_pole_data_to_pubsub(pole_data=request_params, customer_details=customer_details)
        asset_service.update_telemetry(entity_type=ENTITY_TYPE[1], entity_id=switch_point_id, data=telemetry_data)
        # Placing Lamp under region group
        device_service.place_entity_under_group(
            entity_ids=[switch_point_id], owner_type=OWNER_TYPE_CUSTOMER,
            owner_id=customer_id, group_type=ENTITY_TYPE_ASSET, group_name=region)

    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("couldn't install pole asset %s" % str(e))
    return HttpResponse(request_handler.get_service_response())


@token_validator
@method_validate("GET")
def get_switch_point(request):
    request_handler = RequestResponseHandler()
    switch_point_name = None
    try:
        asset_service = AssetService(token=request.headers.get('token'), instance=request_handler)
        device_service = DeviceService(token=request.headers.get('token'), instance=request_handler)
        switch_point_name = request.GET.get("name")
        pole_details = asset_service.get_entity_by_name(entity_type=ENTITY_TYPE[1], name=switch_point_name)
        print("pole_details >>>>>>>>>>>>", pole_details)
        pole_id = pole_details.get("id").get("id")
        print("pole_id >>>>>>>>>>>>", pole_id)
        switch_point_query = asset_service.asset_details_query_construct(entity_id=pole_id, entity_type=ENTITY_TYPE[1])
        print("switch_point_query >>>>>>>>>>>>", switch_point_query)
        switch_point_asset_detail = device_service.find_entities_by_query(query=switch_point_query)
        print("switch_point_asset_detail >>>>>>>>>>>>", switch_point_asset_detail)
        request_handler.update_service_response(
            asset_service.pole_details_construct(data=switch_point_asset_detail['data'], pole_name=switch_point_name))
    except Exception as e:
        request_handler.update_error_response(str(e))
        logger.exception("Couldn't get pole %s ... %s" % (switch_point_name, str(e)))
    return HttpResponse(request_handler.get_service_response())


#!/usr/bin/env python3
"""
Test script for SCHNELLIOT FastAPI application
"""

import requests
import json
import sys
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

def test_health_endpoint():
    """Test health endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            print("✅ Health endpoint working")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_root_endpoint():
    """Test root endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if "SCHNELLIOT API" in data.get("message", ""):
                print("✅ Root endpoint working")
                return True
            else:
                print(f"❌ Root endpoint unexpected response: {data}")
                return False
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
        return False

def test_docs_endpoint():
    """Test API documentation endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=10)
        if response.status_code == 200:
            print("✅ API docs endpoint working")
            return True
        else:
            print(f"❌ API docs endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API docs endpoint error: {e}")
        return False

def test_onboard_ilm_endpoint():
    """Test ILM onboarding endpoint (without authentication)"""
    try:
        # Test with invalid credentials to check endpoint structure
        response = requests.post(
            f"{BASE_URL}/onboard/ilm/invalid/ilm_v1/", 
            timeout=10
        )
        # We expect this to fail but the endpoint should exist
        if response.status_code in [400, 422, 500]:
            print("✅ ILM onboarding endpoint exists and responding")
            return True
        else:
            print(f"❌ ILM onboarding endpoint unexpected status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ ILM onboarding endpoint error: {e}")
        return False

def test_sms_send_endpoint():
    """Test SMS send endpoint (without authentication)"""
    try:
        # Test with minimal data to check endpoint structure
        response = requests.post(
            f"{BASE_URL}/sms/send/",
            data={"mobile": "1234567890", "message": "test"},
            timeout=10
        )
        # We expect this to fail but the endpoint should exist
        if response.status_code in [400, 422, 500]:
            print("✅ SMS send endpoint exists and responding")
            return True
        else:
            print(f"❌ SMS send endpoint unexpected status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ SMS send endpoint error: {e}")
        return False

def test_lume_login_endpoint():
    """Test Lume login endpoint"""
    try:
        # Test with minimal data to check endpoint structure
        response = requests.post(
            f"{BASE_URL}/lume/api/login/",
            data={"username": "test", "password": "test"},
            timeout=10
        )
        # We expect this to fail but the endpoint should exist
        if response.status_code in [400, 401, 422, 500]:
            print("✅ Lume login endpoint exists and responding")
            return True
        else:
            print(f"❌ Lume login endpoint unexpected status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Lume login endpoint error: {e}")
        return False

def run_tests():
    """Run all tests"""
    print("🚀 Starting SCHNELLIOT API Tests...")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_endpoint),
        ("Root Endpoint", test_root_endpoint),
        ("API Documentation", test_docs_endpoint),
        ("ILM Onboarding", test_onboard_ilm_endpoint),
        ("SMS Send", test_sms_send_endpoint),
        ("Lume Login", test_lume_login_endpoint),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Testing {test_name}...")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! API is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the application setup.")
        return False

def check_server_running():
    """Check if the server is running"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        return True
    except:
        return False

if __name__ == "__main__":
    print("SCHNELLIOT API Test Suite")
    print("=" * 50)
    
    if not check_server_running():
        print("❌ Server is not running!")
        print("Please start the server with: uvicorn mobaile_api.main:app --reload")
        sys.exit(1)
    
    success = run_tests()
    sys.exit(0 if success else 1)
